{"version": 3, "sources": ["../../@phosphor-icons/react/dist/ssr/CaretLeft.es.js", "../../@phosphor-icons/react/dist/defs/CaretLeft.es.js"], "sourcesContent": ["import * as e from \"react\";\nimport a from \"../lib/SSRBase.es.js\";\nimport f from \"../defs/CaretLeft.es.js\";\nconst t = e.forwardRef((r, o) => /* @__PURE__ */ e.createElement(a, { ref: o, ...r, weights: f }));\nt.displayName = \"CaretLeftIcon\";\nconst c = t;\nexport {\n  c as CaretLeft,\n  t as CaretLeftIcon\n};\n", "import * as e from \"react\";\nconst a = /* @__PURE__ */ new Map([\n  [\n    \"bold\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M168.49,199.51a12,12,0,0,1-17,17l-80-80a12,12,0,0,1,0-17l80-80a12,12,0,0,1,17,17L97,128Z\" }))\n  ],\n  [\n    \"duotone\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M160,48V208L80,128Z\", opacity: \"0.2\" }), /* @__PURE__ */ e.createElement(\"path\", { d: \"M163.06,40.61a8,8,0,0,0-8.72,1.73l-80,80a8,8,0,0,0,0,11.32l80,80A8,8,0,0,0,168,208V48A8,8,0,0,0,163.06,40.61ZM152,188.69,91.31,128,152,67.31Z\" }))\n  ],\n  [\n    \"fill\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M168,48V208a8,8,0,0,1-13.66,5.66l-80-80a8,8,0,0,1,0-11.32l80-80A8,8,0,0,1,168,48Z\" }))\n  ],\n  [\n    \"light\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M164.24,203.76a6,6,0,1,1-8.48,8.48l-80-80a6,6,0,0,1,0-8.48l80-80a6,6,0,0,1,8.48,8.48L88.49,128Z\" }))\n  ],\n  [\n    \"regular\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M165.66,202.34a8,8,0,0,1-11.32,11.32l-80-80a8,8,0,0,1,0-11.32l80-80a8,8,0,0,1,11.32,11.32L91.31,128Z\" }))\n  ],\n  [\n    \"thin\",\n    /* @__PURE__ */ e.createElement(e.Fragment, null, /* @__PURE__ */ e.createElement(\"path\", { d: \"M162.83,205.17a4,4,0,0,1-5.66,5.66l-80-80a4,4,0,0,1,0-5.66l80-80a4,4,0,1,1,5.66,5.66L85.66,128Z\" }))\n  ]\n]);\nexport {\n  a as default\n};\n"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,KAAmB;;;ACAnB,QAAmB;AACnB,IAAM,IAAoB,oBAAI,IAAI;AAAA,EAChC;AAAA,IACE;AAAA,IACkB,gBAAgB,YAAU,MAAwB,gBAAc,QAAQ,EAAE,GAAG,2FAA2F,CAAC,CAAC;AAAA,EAC9L;AAAA,EACA;AAAA,IACE;AAAA,IACkB,gBAAgB,YAAU,MAAwB,gBAAc,QAAQ,EAAE,GAAG,uBAAuB,SAAS,MAAM,CAAC,GAAqB,gBAAc,QAAQ,EAAE,GAAG,gJAAgJ,CAAC,CAAC;AAAA,EAC1U;AAAA,EACA;AAAA,IACE;AAAA,IACkB,gBAAgB,YAAU,MAAwB,gBAAc,QAAQ,EAAE,GAAG,oFAAoF,CAAC,CAAC;AAAA,EACvL;AAAA,EACA;AAAA,IACE;AAAA,IACkB,gBAAgB,YAAU,MAAwB,gBAAc,QAAQ,EAAE,GAAG,kGAAkG,CAAC,CAAC;AAAA,EACrM;AAAA,EACA;AAAA,IACE;AAAA,IACkB,gBAAgB,YAAU,MAAwB,gBAAc,QAAQ,EAAE,GAAG,uGAAuG,CAAC,CAAC;AAAA,EAC1M;AAAA,EACA;AAAA,IACE;AAAA,IACkB,gBAAgB,YAAU,MAAwB,gBAAc,QAAQ,EAAE,GAAG,kGAAkG,CAAC,CAAC;AAAA,EACrM;AACF,CAAC;;;ADvBD,IAAM,IAAM,cAAW,CAAC,GAAG,MAAwB,iBAAc,GAAG,EAAE,KAAK,GAAG,GAAG,GAAG,SAAS,EAAE,CAAC,CAAC;AACjG,EAAE,cAAc;AAChB,IAAM,IAAI;", "names": ["e"]}