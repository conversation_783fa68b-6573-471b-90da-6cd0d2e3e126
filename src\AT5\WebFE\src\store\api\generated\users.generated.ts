import { baseApi as api } from "../base-api";
const injectedRtkApi = api.injectEndpoints({
  endpoints: (build) => ({
    atApiServiceEndpointsV2UsersGetUserEndpoint: build.query<
      AtApiServiceEndpointsV2UsersGetUserEndpointApiResponse,
      AtApiServiceEndpointsV2UsersGetUserEndpointApiArg
    >({
      query: (queryArg) => ({ url: `/api/v2/users/${queryArg.userId}` }),
    }),
  }),
  overrideExisting: false,
});
export { injectedRtkApi as usersGeneratedApi };
export type AtApiServiceEndpointsV2UsersGetUserEndpointApiResponse =
  /** status 200 Success */ AtApiServiceEndpointsV2UsersModelsGetUserResponse;
export type AtApiServiceEndpointsV2UsersGetUserEndpointApiArg = {
  userId: string;
};
export type AtApiServiceEndpointsV2UsersModelsGetUserResponse = {
  name?: string;
  userId?: string;
};
export const { useAtApiServiceEndpointsV2UsersGetUserEndpointQuery } =
  injectedRtkApi;
