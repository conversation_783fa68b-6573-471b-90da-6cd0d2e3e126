{"version": 3, "sources": ["../../odata-filter-builder/es/ODataFilterBuilder.js"], "sourcesContent": ["var canonicalFunctions = /*#__PURE__*/Object.freeze({\n  __proto__: null,\n  get canonicalFunction () { return canonicalFunction; },\n  get contains () { return contains; },\n  get startsWith () { return startsWith; },\n  get endsWith () { return endsWith; },\n  get toLower () { return toLower; },\n  get toUpper () { return toUpper; },\n  get trim () { return trim; },\n  get substring () { return substring; },\n  get concat () { return concat; },\n  get length () { return length; },\n  get indexOf () { return indexOf; }\n});\n\n/**\n * Reduce source with new rule and/or condition\n * @param {Object} source - Source rule\n * @param {Object|string} rule - Rule to add\n * @param {string} [condition] - Condition for rule to add(and/or)\n * @returns {Object} updated rule\n * @private\n */\nfunction reduceSourceWithRule(source, rule, condition) {\n  if (rule) {\n    if (condition && source.condition !== condition) {\n      // if source rules condition different from rule condition\n      // update source condition\n      source = {\n        condition: condition,\n        // if has more then one rules\n        // regroup source rules tree\n        rules: source.rules.length > 1 ? [source] : source.rules\n      };\n    } // add new rule\n\n\n    source.rules.push(rule);\n  }\n\n  return source;\n}\n\nfunction inputRuleToString(rule) {\n  if (typeof rule === 'function') {\n    rule = rule(new ODataFilterBuilder());\n  }\n\n  return rule && rule.toString();\n}\n\nfunction joinRulesWithCondition(rules, condition) {\n  return rules.map(function (r) {\n    return sourceRuleToString(r, true);\n  }).join(\" \" + condition + \" \");\n}\n\nfunction sourceRuleToString(rule, wrapInParenthesis) {\n  if (wrapInParenthesis === void 0) {\n    wrapInParenthesis = false;\n  }\n\n  if (typeof rule !== 'string') {\n    // if child rules more then one join child rules by condition\n    // and wrap in brackets every child rule\n    rule = rule.rules.length === 1 ? sourceRuleToString(rule.rules[0]) : joinRulesWithCondition(rule.rules, rule.condition);\n  }\n\n  return wrapInParenthesis ? \"(\" + rule + \")\" : rule;\n}\n\nfunction inputFieldToString(field) {\n  return typeof field === 'function' ? field(canonicalFunctions) : field;\n}\n\nfunction isString(value) {\n  return typeof value === 'string';\n}\n\nfunction isDate(value) {\n  return typeof value === 'object' && Object.prototype.toString.call(value) === '[object Date]';\n}\n\nfunction normaliseValue(value) {\n  if (isString(value)) {\n    return \"'\" + value + \"'\";\n  }\n\n  if (isDate(value)) {\n    return value.toISOString();\n  }\n\n  return value;\n}\n\nfunction canonicalFunction(functionName, field, values, normaliseValues, reverse) {\n  if (normaliseValues === void 0) {\n    normaliseValues = true;\n  }\n\n  if (reverse === void 0) {\n    reverse = false;\n  }\n\n  // make sure that field is string\n  field = inputFieldToString(field);\n\n  if (typeof values === 'undefined') {\n    values = [];\n  } else if (!Array.isArray(values)) {\n    values = [values];\n  }\n\n  if (values.length === 0) {\n    return functionName + \"(\" + field + \")\";\n  }\n\n  if (normaliseValues) {\n    values = values.map(normaliseValue);\n  }\n\n  var functionArgs = !reverse ? [field].concat(values) : [].concat(values, [field]);\n  return functionName + \"(\" + functionArgs.join(', ') + \")\";\n}\n\nfunction contains(field, value) {\n  return canonicalFunction('contains', field, value);\n}\n\nfunction startsWith(field, value) {\n  return canonicalFunction('startswith', field, value);\n}\n\nfunction endsWith(field, value) {\n  return canonicalFunction('endswith', field, value);\n}\n/**\n * The tolower function returns the input parameter string value with all uppercase characters converted to lowercase.\n * @example\n * f().eq(x => x.toLower('CompanyName'), 'alfreds futterkiste')\n * // tolower(CompanyName) eq 'alfreds futterkiste'\n * @param {string|InputFieldExpression} field - Field\n * @returns {string} A function string\n */\n\n\nfunction toLower(field) {\n  return canonicalFunction('tolower', field);\n}\n/**\n * The toupper function returns the input parameter string value with all lowercase characters converted to uppercase.\n * @example\n * f().eq(x => x.toUpper('CompanyName'), 'ALFREDS FUTTERKISTE')\n * // toupper(CompanyName) eq 'ALFREDS FUTTERKISTE'\n * @param {string|InputFieldExpression} field - Field\n * @returns {string} A function string\n */\n\n\nfunction toUpper(field) {\n  return canonicalFunction('toupper', field);\n}\n/**\n * The trim function returns the input parameter string value with all leading and trailing whitespace characters, removed.\n * @example\n * f().eq(x => x.trim('CompanyName'), 'CompanyName')\n * // trim(CompanyName) eq CompanyName\n * @param {string|InputFieldExpression} field - Field\n * @returns {string} A function string\n */\n\n\nfunction trim(field) {\n  return canonicalFunction('trim', field);\n}\n/**\n * @example\n * f().eq(f.functions.substring('CompanyName', 1), 'lfreds Futterkiste');\n * f().eq(x => x.substring('CompanyName', 1), 'lfreds Futterkiste');\n * // substring(CompanyName, 1) eq 'lfreds Futterkiste'\n *\n * @example\n * f().eq(x => x.substring('CompanyName', 1, 2), 'lf').toString();\n * f().eq(f.functions.substring('CompanyName', 1, 2), 'lf')\n * // substring(CompanyName, 1, 2) eq 'lf'\n *\n * @param {string|InputFieldExpression} field - The first function parameter\n * @param {...number} values - Second or second and third function parameters\n *\n * @returns {string} A function string\n */\n\n\nfunction substring(field) {\n  for (var _len = arguments.length, values = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    values[_key - 1] = arguments[_key];\n  }\n\n  return canonicalFunction('substring', field, values);\n}\n/**\n * @param {string|InputFieldExpression} field - The first function parameter\n * @param {string} value - The second function parameter\n * @param {boolean} [normaliseValue=true] - Convert string \"value\" to \"'value'\" or not. (Convert by default)\n * @example\n * f().eq(x => x.concat(y => y.concat('City',', '), 'Country', false), 'Berlin, Germany');\n * // concat(concat(City, ', '), 'Country') eq 'Berlin, Germany'\n * @returns {string} A function string\n */\n\n\nfunction concat(field, value, normaliseValue) {\n  return canonicalFunction('concat', field, [value], normaliseValue);\n}\n/**\n * The length function returns the number of characters in the parameter value.\n * @example\n * f().eq(x => x.length('CompanyName'), 19)\n * // length(CompanyName) eq 19\n * @param {string|InputFieldExpression} field - Field\n * @returns {string} A function string\n */\n\n\nfunction length(field) {\n  return canonicalFunction('length', field);\n}\n/**\n * The indexof function returns the zero-based character position of the first occurrence of the second parameter value in the first parameter value.\n * @example\n * f().eq(f.functions.indexOf('CompanyName', 'lfreds'), 1)\n * f().eq(x => x.indexOf('CompanyName', 'lfreds'), 1)\n * // indexof(CompanyName,'lfreds') eq 1\n *\n * @param {string|InputFieldExpression} field - The first function parameter\n * @param {string} value - The second function parameter\n *\n * @returns {string} A function string\n */\n\n\nfunction indexOf(field, value) {\n  return canonicalFunction('indexof', field, [value]);\n}\n\nfunction not(rule) {\n  var ruleString = inputRuleToString(rule);\n\n  if (ruleString) {\n    return \"not (\" + ruleString + \")\";\n  }\n}\n\nfunction compare(field, operator, value, normaliseValue$1) {\n  if (normaliseValue$1 === void 0) {\n    normaliseValue$1 = true;\n  }\n\n  // make sure that field is string\n  field = inputFieldToString(field);\n\n  if (normaliseValue$1) {\n    value = normaliseValue(value);\n  }\n\n  return field + \" \" + operator + \" \" + value;\n}\n\nfunction compareMap(field, operator, values, normaliseValues) {\n  if (normaliseValues === void 0) {\n    normaliseValues = true;\n  }\n\n  if (!values) {\n    return [];\n  } // make sure that field is string\n\n\n  field = inputFieldToString(field);\n\n  if (!Array.isArray(values)) {\n    return [compare(field, operator, values, normaliseValues)];\n  }\n\n  return values.map(function (value) {\n    return compare(field, operator, value, normaliseValues);\n  });\n}\n\nfunction eq(field, value, normaliseValue) {\n  return compare(field, 'eq', value, normaliseValue);\n}\n\nfunction ne(field, value, normaliseValue) {\n  return compare(field, 'ne', value, normaliseValue);\n}\n\nfunction gt(field, value, normaliseValue) {\n  return compare(field, 'gt', value, normaliseValue);\n}\n\nfunction ge(field, value, normaliseValue) {\n  return compare(field, 'ge', value, normaliseValue);\n}\n\nfunction lt(field, value, normaliseValue) {\n  return compare(field, 'lt', value, normaliseValue);\n}\n\nfunction le(field, value, normaliseValue) {\n  return compare(field, 'le', value, normaliseValue);\n}\n\nfunction joinRules(rules, condition) {\n  return rules.join(\" \" + condition + \" \");\n}\n\nfunction compareIn(field, values, normaliseValues) {\n  return joinRules(compareMap(field, 'eq', values, normaliseValues), 'or');\n}\n\nfunction compareAll(objectValue, normaliseValues) {\n  var keys = Object.keys(objectValue);\n  var rules = keys.filter(function (k) {\n    return typeof objectValue[k] !== 'undefined';\n  }).map(function (field) {\n    var value = objectValue[field];\n\n    if (Array.isArray(value)) {\n      return \"(\" + compareIn(field, value, normaliseValues) + \")\";\n    } else {\n      return eq(field, value, normaliseValues);\n    }\n  });\n  return joinRules(rules, 'and');\n}\n\nfunction compareNotIn(field, values, normaliseValues) {\n  // return joinRules(compareMap(field, 'ne', values, normaliseValues), 'and')\n  return not(compareIn(field, values, normaliseValues));\n}\n\nvar ODataFilterBuilder =\n/*#__PURE__*/\nfunction () {\n  function ODataFilterBuilder(condition) {\n    if (condition === void 0) {\n      condition = 'and';\n    }\n\n    if (!(this instanceof ODataFilterBuilder)) {\n      return new ODataFilterBuilder(condition);\n    }\n\n    this._condition = condition;\n    this._source = {\n      condition: condition,\n      rules: []\n    };\n  }\n  /**\n   * The 'add' method adds new filter rule with AND or OR condition\n   * if condition not provided. Source condition is used (AND by default)\n   * @this {ODataFilterBuilder}\n   * @param {string|ODataFilterBuilder|InputRuleExpression} rule - Rule to add\n   * @param {string} [condition] - Condition for rule to add(and/or)\n   * @returns {ODataFilterBuilder} The {@link ODataFilterBuilder} instance\n   * @private\n   */\n\n\n  var _proto = ODataFilterBuilder.prototype;\n\n  _proto._add = function _add(rule, condition) {\n    if (condition === void 0) {\n      condition = this._condition;\n    }\n\n    // NOTE: if condition not provider, source condition uses\n    this._source = reduceSourceWithRule(this._source, inputRuleToString(rule), condition);\n    return this;\n  }\n  /*\n   * Logical Operators\n   */\n\n  /**\n   * Logical And\n   * @param {string|ODataFilterBuilder|InputRuleExpression} rule - Rule to add\n   * @returns {ODataFilterBuilder} The {@link ODataFilterBuilder} instance\n   */\n  ;\n\n  _proto.and = function and(rule) {\n    return this._add(rule, 'and');\n  }\n  /**\n   * Logical Or\n   * @param {string|ODataFilterBuilder|InputRuleExpression} rule - Rule to add\n   * @returns {ODataFilterBuilder} The {@link ODataFilterBuilder} instance\n   */\n  ;\n\n  _proto.or = function or(rule) {\n    return this._add(rule, 'or');\n  }\n  /**\n   * Logical Negation\n   * @param {string|ODataFilterBuilder|InputRuleExpression} rule - Rule to add\n   * @returns {ODataFilterBuilder} The {@link ODataFilterBuilder} instance\n   */\n  ;\n\n  _proto.not = function not$1(rule) {\n    return this._add(not(rule));\n  }\n  /**\n   * Equal\n   * @param {string|InputFieldExpression} field - Field to compare\n   * @param {string|number|*} value - A value to compare with\n   * @param {boolean} [normaliseValue=true] - Convert string \"value\" to \"'value'\" or not. (Convert by default)\n   * @returns {ODataFilterBuilder} The {@link ODataFilterBuilder} instance\n   */\n  ;\n\n  _proto.eq = function eq$1(field, value, normaliseValue) {\n    return this._add(eq(field, value, normaliseValue));\n  }\n  /**\n   * Not Equal\n   * @param {string|InputFieldExpression} field - Field to compare\n   * @param {string|number|*} value - A value to compare with\n   * @param {boolean} [normaliseValue=true] - Convert string \"value\" to \"'value'\" or not. (Convert by default)\n   * @returns {ODataFilterBuilder} The {@link ODataFilterBuilder} instance\n   */\n  ;\n\n  _proto.ne = function ne$1(field, value, normaliseValue) {\n    return this._add(ne(field, value, normaliseValue));\n  }\n  /**\n   * Greater Than\n   * @param {string|InputFieldExpression} field - Field to compare\n   * @param {string|number|*} value - A value to compare with\n   * @param {boolean} [normaliseValue=true] - Convert string \"value\" to \"'value'\" or not. (Convert by default)\n   * @returns {ODataFilterBuilder} The {@link ODataFilterBuilder} instance\n   */\n  ;\n\n  _proto.gt = function gt$1(field, value, normaliseValue) {\n    return this._add(gt(field, value, normaliseValue));\n  }\n  /**\n   * Greater than or Equal\n   * @param {string|InputFieldExpression} field - Field to compare\n   * @param {string|number|*} value - A value to compare with\n   * @param {boolean} [normaliseValue=true] - Convert string \"value\" to \"'value'\" or not. (Convert by default)\n   * @returns {ODataFilterBuilder} The {@link ODataFilterBuilder} instance\n   */\n  ;\n\n  _proto.ge = function ge$1(field, value, normaliseValue) {\n    return this._add(ge(field, value, normaliseValue));\n  }\n  /**\n   * Less Than\n   * @param {string|InputFieldExpression} field - Field to compare\n   * @param {string|number|*} value - A value to compare with\n   * @param {boolean} [normaliseValue=true] - Convert string \"value\" to \"'value'\" or not. (Convert by default)\n   * @returns {ODataFilterBuilder} The {@link ODataFilterBuilder} instance\n   */\n  ;\n\n  _proto.lt = function lt$1(field, value, normaliseValue) {\n    return this._add(lt(field, value, normaliseValue));\n  }\n  /**\n   * Less than or Equal\n   * @param {string|InputFieldExpression} field - Field to compare\n   * @param {string|number|*} value - A value to compare with\n   * @param {boolean} [normaliseValue=true] - Convert string \"value\" to \"'value'\" or not. (Convert by default)\n   * @returns {ODataFilterBuilder} The {@link ODataFilterBuilder} instance\n   */\n  ;\n\n  _proto.le = function le$1(field, value, normaliseValue) {\n    return this._add(le(field, value, normaliseValue));\n  }\n  /**\n   * @param {string|InputFieldExpression} field - Field to compare\n   * @param {string[]|string} values - Values to compare with\n   * @param {boolean} [normaliseValues=true] - Convert string \"value\" to \"'value'\" or not. (Convert by default)\n   * @returns {ODataFilterBuilder} The {@link ODataFilterBuilder} instance\n   */\n  ;\n\n  _proto[\"in\"] = function _in(field, values, normaliseValues) {\n    return this._add(compareIn(field, values, normaliseValues));\n  }\n  /**\n   * @param {any} objectValue - Object with property and value to compare all in \"and\" - Loops through property keys\n   * @param {boolean} [normaliseValues=true] - Convert string \"value\" to \"'value'\" or not. (Convert by default)\n   * @returns {ODataFilterBuilder} The {@link ODataFilterBuilder} instance\n   */\n  ;\n\n  _proto.compareAll = function compareAll$1(objectValue, normaliseValues) {\n    return this._add(compareAll(objectValue, normaliseValues));\n  }\n  /**\n   * @param {string|InputFieldExpression} field - Field to compare\n   * @param {Array} values - Values to compare with\n   * @param {boolean} [normaliseValues=true] - Convert string \"value\" to \"'value'\" or not. (Convert by default)\n   * @returns {ODataFilterBuilder} The {@link ODataFilterBuilder} instance\n   */\n  ;\n\n  _proto.notIn = function notIn(field, values, normaliseValues) {\n    return this._add(compareNotIn(field, values, normaliseValues));\n  } // Canonical Functions\n\n  /**\n   * The contains function returns true if the second parameter string value is a substring of the first parameter string value.\n   * @param {string|InputFieldExpression} field - Field to compare\n   * @param {string} value - Value to compare\n   * @returns {ODataFilterBuilder} The {@link ODataFilterBuilder} instance\n   */\n  ;\n\n  _proto.contains = function contains$1(field, value) {\n    return this._add(contains(field, value));\n  }\n  /**\n   * The startswith function returns true if the first parameter string value starts with the second parameter string value.\n   * @param {string|InputFieldExpression} field - Field to compare\n   * @param {string} value - Value to compare\n   * @returns {ODataFilterBuilder} The {@link ODataFilterBuilder} instance\n   */\n  ;\n\n  _proto.startsWith = function startsWith$1(field, value) {\n    return this._add(startsWith(field, value));\n  }\n  /**\n   * The endswith function returns true if the first parameter string value ends with the second parameter string value.\n   * @param {string|InputFieldExpression} field - Field to compare\n   * @param {string} value - Value to compare\n   * @returns {ODataFilterBuilder} The {@link ODataFilterBuilder} instance\n   */\n  ;\n\n  _proto.endsWith = function endsWith$1(field, value) {\n    return this._add(endsWith(field, value));\n  }\n  /**\n   * Custom function\n   * @param {string} functionName - Name of generated function\n   * @param {string|InputFieldExpression} field - The first function parameter\n   * @param {string|number|Array} values - The second function parameter\n   * @param {boolean} [normaliseValues=true] - Convert string \"value\" to \"'value'\" or not. (Convert by default)\n   * @param {boolean} [reverse=false] - Swap field and value params in output. (Don't swap by default)\n   * @returns {*|ODataFilterBuilder} The {@link ODataFilterBuilder} instance\n   */\n  ;\n\n  _proto.fn = function fn(functionName, field, values, normaliseValues, reverse) {\n    return this._add(canonicalFunction(functionName, field, values, normaliseValues, reverse));\n  };\n\n  _proto.isEmpty = function isEmpty() {\n    return this._source.rules.length === 0;\n  }\n  /**\n   * Convert filter builder instance to string\n   * @this {ODataFilterBuilder}\n   * @returns {string} A source string representation\n   */\n  ;\n\n  _proto.toString = function toString() {\n    return sourceRuleToString(this._source);\n  };\n\n  return ODataFilterBuilder;\n}();\n\nODataFilterBuilder.and = function () {\n  return new ODataFilterBuilder('and');\n};\n\nODataFilterBuilder.or = function () {\n  return new ODataFilterBuilder('or');\n};\n\nODataFilterBuilder.functions = canonicalFunctions;\n\nexport default ODataFilterBuilder;\nexport { ODataFilterBuilder, canonicalFunctions };\n"], "mappings": ";;;AAAA,IAAI,qBAAkC,OAAO,OAAO;AAAA,EAClD,WAAW;AAAA,EACX,IAAI,oBAAqB;AAAE,WAAO;AAAA,EAAmB;AAAA,EACrD,IAAI,WAAY;AAAE,WAAO;AAAA,EAAU;AAAA,EACnC,IAAI,aAAc;AAAE,WAAO;AAAA,EAAY;AAAA,EACvC,IAAI,WAAY;AAAE,WAAO;AAAA,EAAU;AAAA,EACnC,IAAI,UAAW;AAAE,WAAO;AAAA,EAAS;AAAA,EACjC,IAAI,UAAW;AAAE,WAAO;AAAA,EAAS;AAAA,EACjC,IAAI,OAAQ;AAAE,WAAO;AAAA,EAAM;AAAA,EAC3B,IAAI,YAAa;AAAE,WAAO;AAAA,EAAW;AAAA,EACrC,IAAI,SAAU;AAAE,WAAO;AAAA,EAAQ;AAAA,EAC/B,IAAI,SAAU;AAAE,WAAO;AAAA,EAAQ;AAAA,EAC/B,IAAI,UAAW;AAAE,WAAO;AAAA,EAAS;AACnC,CAAC;AAUD,SAAS,qBAAqB,QAAQ,MAAM,WAAW;AACrD,MAAI,MAAM;AACR,QAAI,aAAa,OAAO,cAAc,WAAW;AAG/C,eAAS;AAAA,QACP;AAAA;AAAA;AAAA,QAGA,OAAO,OAAO,MAAM,SAAS,IAAI,CAAC,MAAM,IAAI,OAAO;AAAA,MACrD;AAAA,IACF;AAGA,WAAO,MAAM,KAAK,IAAI;AAAA,EACxB;AAEA,SAAO;AACT;AAEA,SAAS,kBAAkB,MAAM;AAC/B,MAAI,OAAO,SAAS,YAAY;AAC9B,WAAO,KAAK,IAAI,mBAAmB,CAAC;AAAA,EACtC;AAEA,SAAO,QAAQ,KAAK,SAAS;AAC/B;AAEA,SAAS,uBAAuB,OAAO,WAAW;AAChD,SAAO,MAAM,IAAI,SAAU,GAAG;AAC5B,WAAO,mBAAmB,GAAG,IAAI;AAAA,EACnC,CAAC,EAAE,KAAK,MAAM,YAAY,GAAG;AAC/B;AAEA,SAAS,mBAAmB,MAAM,mBAAmB;AACnD,MAAI,sBAAsB,QAAQ;AAChC,wBAAoB;AAAA,EACtB;AAEA,MAAI,OAAO,SAAS,UAAU;AAG5B,WAAO,KAAK,MAAM,WAAW,IAAI,mBAAmB,KAAK,MAAM,CAAC,CAAC,IAAI,uBAAuB,KAAK,OAAO,KAAK,SAAS;AAAA,EACxH;AAEA,SAAO,oBAAoB,MAAM,OAAO,MAAM;AAChD;AAEA,SAAS,mBAAmB,OAAO;AACjC,SAAO,OAAO,UAAU,aAAa,MAAM,kBAAkB,IAAI;AACnE;AAEA,SAAS,SAAS,OAAO;AACvB,SAAO,OAAO,UAAU;AAC1B;AAEA,SAAS,OAAO,OAAO;AACrB,SAAO,OAAO,UAAU,YAAY,OAAO,UAAU,SAAS,KAAK,KAAK,MAAM;AAChF;AAEA,SAAS,eAAe,OAAO;AAC7B,MAAI,SAAS,KAAK,GAAG;AACnB,WAAO,MAAM,QAAQ;AAAA,EACvB;AAEA,MAAI,OAAO,KAAK,GAAG;AACjB,WAAO,MAAM,YAAY;AAAA,EAC3B;AAEA,SAAO;AACT;AAEA,SAAS,kBAAkB,cAAc,OAAO,QAAQ,iBAAiB,SAAS;AAChF,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AAEA,MAAI,YAAY,QAAQ;AACtB,cAAU;AAAA,EACZ;AAGA,UAAQ,mBAAmB,KAAK;AAEhC,MAAI,OAAO,WAAW,aAAa;AACjC,aAAS,CAAC;AAAA,EACZ,WAAW,CAAC,MAAM,QAAQ,MAAM,GAAG;AACjC,aAAS,CAAC,MAAM;AAAA,EAClB;AAEA,MAAI,OAAO,WAAW,GAAG;AACvB,WAAO,eAAe,MAAM,QAAQ;AAAA,EACtC;AAEA,MAAI,iBAAiB;AACnB,aAAS,OAAO,IAAI,cAAc;AAAA,EACpC;AAEA,MAAI,eAAe,CAAC,UAAU,CAAC,KAAK,EAAE,OAAO,MAAM,IAAI,CAAC,EAAE,OAAO,QAAQ,CAAC,KAAK,CAAC;AAChF,SAAO,eAAe,MAAM,aAAa,KAAK,IAAI,IAAI;AACxD;AAEA,SAAS,SAAS,OAAO,OAAO;AAC9B,SAAO,kBAAkB,YAAY,OAAO,KAAK;AACnD;AAEA,SAAS,WAAW,OAAO,OAAO;AAChC,SAAO,kBAAkB,cAAc,OAAO,KAAK;AACrD;AAEA,SAAS,SAAS,OAAO,OAAO;AAC9B,SAAO,kBAAkB,YAAY,OAAO,KAAK;AACnD;AAWA,SAAS,QAAQ,OAAO;AACtB,SAAO,kBAAkB,WAAW,KAAK;AAC3C;AAWA,SAAS,QAAQ,OAAO;AACtB,SAAO,kBAAkB,WAAW,KAAK;AAC3C;AAWA,SAAS,KAAK,OAAO;AACnB,SAAO,kBAAkB,QAAQ,KAAK;AACxC;AAmBA,SAAS,UAAU,OAAO;AACxB,WAAS,OAAO,UAAU,QAAQ,SAAS,IAAI,MAAM,OAAO,IAAI,OAAO,IAAI,CAAC,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC5G,WAAO,OAAO,CAAC,IAAI,UAAU,IAAI;AAAA,EACnC;AAEA,SAAO,kBAAkB,aAAa,OAAO,MAAM;AACrD;AAYA,SAAS,OAAO,OAAO,OAAOA,iBAAgB;AAC5C,SAAO,kBAAkB,UAAU,OAAO,CAAC,KAAK,GAAGA,eAAc;AACnE;AAWA,SAAS,OAAO,OAAO;AACrB,SAAO,kBAAkB,UAAU,KAAK;AAC1C;AAeA,SAAS,QAAQ,OAAO,OAAO;AAC7B,SAAO,kBAAkB,WAAW,OAAO,CAAC,KAAK,CAAC;AACpD;AAEA,SAAS,IAAI,MAAM;AACjB,MAAI,aAAa,kBAAkB,IAAI;AAEvC,MAAI,YAAY;AACd,WAAO,UAAU,aAAa;AAAA,EAChC;AACF;AAEA,SAAS,QAAQ,OAAO,UAAU,OAAO,kBAAkB;AACzD,MAAI,qBAAqB,QAAQ;AAC/B,uBAAmB;AAAA,EACrB;AAGA,UAAQ,mBAAmB,KAAK;AAEhC,MAAI,kBAAkB;AACpB,YAAQ,eAAe,KAAK;AAAA,EAC9B;AAEA,SAAO,QAAQ,MAAM,WAAW,MAAM;AACxC;AAEA,SAAS,WAAW,OAAO,UAAU,QAAQ,iBAAiB;AAC5D,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AAEA,MAAI,CAAC,QAAQ;AACX,WAAO,CAAC;AAAA,EACV;AAGA,UAAQ,mBAAmB,KAAK;AAEhC,MAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,WAAO,CAAC,QAAQ,OAAO,UAAU,QAAQ,eAAe,CAAC;AAAA,EAC3D;AAEA,SAAO,OAAO,IAAI,SAAU,OAAO;AACjC,WAAO,QAAQ,OAAO,UAAU,OAAO,eAAe;AAAA,EACxD,CAAC;AACH;AAEA,SAAS,GAAG,OAAO,OAAOA,iBAAgB;AACxC,SAAO,QAAQ,OAAO,MAAM,OAAOA,eAAc;AACnD;AAEA,SAAS,GAAG,OAAO,OAAOA,iBAAgB;AACxC,SAAO,QAAQ,OAAO,MAAM,OAAOA,eAAc;AACnD;AAEA,SAAS,GAAG,OAAO,OAAOA,iBAAgB;AACxC,SAAO,QAAQ,OAAO,MAAM,OAAOA,eAAc;AACnD;AAEA,SAAS,GAAG,OAAO,OAAOA,iBAAgB;AACxC,SAAO,QAAQ,OAAO,MAAM,OAAOA,eAAc;AACnD;AAEA,SAAS,GAAG,OAAO,OAAOA,iBAAgB;AACxC,SAAO,QAAQ,OAAO,MAAM,OAAOA,eAAc;AACnD;AAEA,SAAS,GAAG,OAAO,OAAOA,iBAAgB;AACxC,SAAO,QAAQ,OAAO,MAAM,OAAOA,eAAc;AACnD;AAEA,SAAS,UAAU,OAAO,WAAW;AACnC,SAAO,MAAM,KAAK,MAAM,YAAY,GAAG;AACzC;AAEA,SAAS,UAAU,OAAO,QAAQ,iBAAiB;AACjD,SAAO,UAAU,WAAW,OAAO,MAAM,QAAQ,eAAe,GAAG,IAAI;AACzE;AAEA,SAAS,WAAW,aAAa,iBAAiB;AAChD,MAAI,OAAO,OAAO,KAAK,WAAW;AAClC,MAAI,QAAQ,KAAK,OAAO,SAAU,GAAG;AACnC,WAAO,OAAO,YAAY,CAAC,MAAM;AAAA,EACnC,CAAC,EAAE,IAAI,SAAU,OAAO;AACtB,QAAI,QAAQ,YAAY,KAAK;AAE7B,QAAI,MAAM,QAAQ,KAAK,GAAG;AACxB,aAAO,MAAM,UAAU,OAAO,OAAO,eAAe,IAAI;AAAA,IAC1D,OAAO;AACL,aAAO,GAAG,OAAO,OAAO,eAAe;AAAA,IACzC;AAAA,EACF,CAAC;AACD,SAAO,UAAU,OAAO,KAAK;AAC/B;AAEA,SAAS,aAAa,OAAO,QAAQ,iBAAiB;AAEpD,SAAO,IAAI,UAAU,OAAO,QAAQ,eAAe,CAAC;AACtD;AAEA,IAAI,qBAEJ,WAAY;AACV,WAASC,oBAAmB,WAAW;AACrC,QAAI,cAAc,QAAQ;AACxB,kBAAY;AAAA,IACd;AAEA,QAAI,EAAE,gBAAgBA,sBAAqB;AACzC,aAAO,IAAIA,oBAAmB,SAAS;AAAA,IACzC;AAEA,SAAK,aAAa;AAClB,SAAK,UAAU;AAAA,MACb;AAAA,MACA,OAAO,CAAC;AAAA,IACV;AAAA,EACF;AAYA,MAAI,SAASA,oBAAmB;AAEhC,SAAO,OAAO,SAAS,KAAK,MAAM,WAAW;AAC3C,QAAI,cAAc,QAAQ;AACxB,kBAAY,KAAK;AAAA,IACnB;AAGA,SAAK,UAAU,qBAAqB,KAAK,SAAS,kBAAkB,IAAI,GAAG,SAAS;AACpF,WAAO;AAAA,EACT;AAYA,SAAO,MAAM,SAAS,IAAI,MAAM;AAC9B,WAAO,KAAK,KAAK,MAAM,KAAK;AAAA,EAC9B;AAQA,SAAO,KAAK,SAAS,GAAG,MAAM;AAC5B,WAAO,KAAK,KAAK,MAAM,IAAI;AAAA,EAC7B;AAQA,SAAO,MAAM,SAAS,MAAM,MAAM;AAChC,WAAO,KAAK,KAAK,IAAI,IAAI,CAAC;AAAA,EAC5B;AAUA,SAAO,KAAK,SAAS,KAAK,OAAO,OAAOD,iBAAgB;AACtD,WAAO,KAAK,KAAK,GAAG,OAAO,OAAOA,eAAc,CAAC;AAAA,EACnD;AAUA,SAAO,KAAK,SAAS,KAAK,OAAO,OAAOA,iBAAgB;AACtD,WAAO,KAAK,KAAK,GAAG,OAAO,OAAOA,eAAc,CAAC;AAAA,EACnD;AAUA,SAAO,KAAK,SAAS,KAAK,OAAO,OAAOA,iBAAgB;AACtD,WAAO,KAAK,KAAK,GAAG,OAAO,OAAOA,eAAc,CAAC;AAAA,EACnD;AAUA,SAAO,KAAK,SAAS,KAAK,OAAO,OAAOA,iBAAgB;AACtD,WAAO,KAAK,KAAK,GAAG,OAAO,OAAOA,eAAc,CAAC;AAAA,EACnD;AAUA,SAAO,KAAK,SAAS,KAAK,OAAO,OAAOA,iBAAgB;AACtD,WAAO,KAAK,KAAK,GAAG,OAAO,OAAOA,eAAc,CAAC;AAAA,EACnD;AAUA,SAAO,KAAK,SAAS,KAAK,OAAO,OAAOA,iBAAgB;AACtD,WAAO,KAAK,KAAK,GAAG,OAAO,OAAOA,eAAc,CAAC;AAAA,EACnD;AASA,SAAO,IAAI,IAAI,SAAS,IAAI,OAAO,QAAQ,iBAAiB;AAC1D,WAAO,KAAK,KAAK,UAAU,OAAO,QAAQ,eAAe,CAAC;AAAA,EAC5D;AAQA,SAAO,aAAa,SAAS,aAAa,aAAa,iBAAiB;AACtE,WAAO,KAAK,KAAK,WAAW,aAAa,eAAe,CAAC;AAAA,EAC3D;AASA,SAAO,QAAQ,SAAS,MAAM,OAAO,QAAQ,iBAAiB;AAC5D,WAAO,KAAK,KAAK,aAAa,OAAO,QAAQ,eAAe,CAAC;AAAA,EAC/D;AAUA,SAAO,WAAW,SAAS,WAAW,OAAO,OAAO;AAClD,WAAO,KAAK,KAAK,SAAS,OAAO,KAAK,CAAC;AAAA,EACzC;AASA,SAAO,aAAa,SAAS,aAAa,OAAO,OAAO;AACtD,WAAO,KAAK,KAAK,WAAW,OAAO,KAAK,CAAC;AAAA,EAC3C;AASA,SAAO,WAAW,SAAS,WAAW,OAAO,OAAO;AAClD,WAAO,KAAK,KAAK,SAAS,OAAO,KAAK,CAAC;AAAA,EACzC;AAYA,SAAO,KAAK,SAAS,GAAG,cAAc,OAAO,QAAQ,iBAAiB,SAAS;AAC7E,WAAO,KAAK,KAAK,kBAAkB,cAAc,OAAO,QAAQ,iBAAiB,OAAO,CAAC;AAAA,EAC3F;AAEA,SAAO,UAAU,SAAS,UAAU;AAClC,WAAO,KAAK,QAAQ,MAAM,WAAW;AAAA,EACvC;AAQA,SAAO,WAAW,SAAS,WAAW;AACpC,WAAO,mBAAmB,KAAK,OAAO;AAAA,EACxC;AAEA,SAAOC;AACT,EAAE;AAEF,mBAAmB,MAAM,WAAY;AACnC,SAAO,IAAI,mBAAmB,KAAK;AACrC;AAEA,mBAAmB,KAAK,WAAY;AAClC,SAAO,IAAI,mBAAmB,IAAI;AACpC;AAEA,mBAAmB,YAAY;AAE/B,IAAO,6BAAQ;", "names": ["normaliseValue", "ODataFilterBuilder"]}