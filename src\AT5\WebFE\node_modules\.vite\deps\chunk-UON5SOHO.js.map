{"version": 3, "sources": ["../../@mui/material/node_modules/@mui/utils/esm/refType/refType.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nconst refType = PropTypes.oneOfType([PropTypes.func, PropTypes.object]);\nexport default refType;"], "mappings": ";;;;;;;;AAAA,wBAAsB;AACtB,IAAM,UAAU,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AACtE,IAAO,kBAAQ;", "names": ["PropTypes"]}