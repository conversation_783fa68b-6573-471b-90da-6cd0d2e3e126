import {
  _extends
} from "./chunk-EQCCHGRT.js";
import "./chunk-LK32TJAX.js";

// node_modules/@mui/x-telemetry/esm/context.js
var context_default = {
  "config": {
    "isInitialized": true
  },
  "traits": {
    "isDocker": false,
    "isCI": false,
    "machineId": null,
    "projectId": "b28c94b2195c8ed259f0b415aaee3f39b0b2920a4537611499fa044956917a21",
    "sessionId": "0c8a9af7f08aebca2317cee1e91f9c7f7d1edf1328febca405653da813413b07",
    "anonymousId": "e337d8cd5eef3e30c5ad11d9580b72817bbdca6738bf9e40faf2d60802e3dcfa"
  }
};

// node_modules/@mui/x-telemetry/esm/runtime/window-storage.js
var prefix = "__mui_x_telemetry_";
function getStorageKey(key) {
  return prefix + btoa(key);
}
function setWindowStorageItem(type, key, value) {
  try {
    if (typeof window !== "undefined" && window[type]) {
      window[type].setItem(getStorageKey(key), value);
      return true;
    }
  } catch (_) {
  }
  return false;
}
function getWindowStorageItem(type, key) {
  try {
    if (typeof window !== "undefined" && window[type]) {
      return window[type].getItem(getStorageKey(key));
    }
  } catch (_) {
  }
  return null;
}

// node_modules/@mui/x-telemetry/esm/runtime/get-context.js
function generateId(length) {
  let result = "";
  const characters = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
  const charactersLength = characters.length;
  let counter = 0;
  while (counter < length) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength));
    counter += 1;
  }
  return result;
}
function pick(obj, keys) {
  return keys.reduce((acc, key) => {
    acc[key] = obj[key];
    return acc;
  }, {});
}
var getBrowserFingerprint = typeof window === "undefined" || false ? () => void 0 : async () => {
  const fingerprintLCKey = "fingerprint";
  try {
    const existingFingerprint = getWindowStorageItem("localStorage", fingerprintLCKey);
    if (existingFingerprint) {
      return JSON.parse(existingFingerprint);
    }
    const FingerprintJS = await import("./fp.esm-SJ4WWGHF.js");
    const fp = await FingerprintJS.load({
      monitoring: false
    });
    const fpResult = await fp.get();
    const components = _extends({}, fpResult.components);
    delete components.cookiesEnabled;
    const fullHash = FingerprintJS.hashComponents(components);
    const coreHash = FingerprintJS.hashComponents(_extends({}, pick(components, ["fonts", "audio", "languages", "deviceMemory", "timezone", "sessionStorage", "localStorage", "indexedDB", "openDatabase", "platform", "canvas", "vendor", "vendorFlavors", "colorGamut", "forcedColors", "monochrome", "contrast", "reducedMotion", "math", "videoCard", "architecture"])));
    const result = {
      fullHash,
      coreHash
    };
    setWindowStorageItem("localStorage", fingerprintLCKey, JSON.stringify(result));
    return result;
  } catch (_) {
    return null;
  }
};
function getAnonymousId() {
  const localStorageKey = "anonymous_id";
  const existingAnonymousId = getWindowStorageItem("localStorage", localStorageKey);
  if (existingAnonymousId) {
    return existingAnonymousId;
  }
  const generated = `anid_${generateId(32)}`;
  if (setWindowStorageItem("localStorage", localStorageKey, generated)) {
    return generated;
  }
  return "";
}
function getSessionId() {
  const localStorageKey = "session_id";
  const existingSessionId = getWindowStorageItem("sessionStorage", localStorageKey);
  if (existingSessionId) {
    return existingSessionId;
  }
  const generated = `sesid_${generateId(32)}`;
  if (setWindowStorageItem("sessionStorage", localStorageKey, generated)) {
    return generated;
  }
  return `sestp_${generateId(32)}`;
}
async function getTelemetryContext() {
  context_default.traits.sessionId = getSessionId();
  if (!context_default.config.isInitialized) {
    context_default.traits.anonymousId = getAnonymousId();
    context_default.config.isInitialized = true;
  }
  if (!context_default.traits.fingerprint) {
    context_default.traits.fingerprint = await getBrowserFingerprint();
  }
  return context_default;
}
var get_context_default = getTelemetryContext;
export {
  get_context_default as default
};
//# sourceMappingURL=get-context-SAWD7VKX.js.map
