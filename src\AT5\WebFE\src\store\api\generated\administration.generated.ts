import { baseApi as api } from "../base-api";
const injectedRtkApi = api.injectEndpoints({
  endpoints: (build) => ({
    atApiServiceEndpointsV2AdministrationJobsDeleteJobEndpoint: build.mutation<
      AtApiServiceEndpointsV2AdministrationJobsDeleteJobEndpointApiResponse,
      AtApiServiceEndpointsV2AdministrationJobsDeleteJobEndpointApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/jobs/${queryArg.entityId}`,
        method: "DELETE",
      }),
    }),
    atApiServiceEndpointsV2AdministrationJobsPutJobEndpoint: build.mutation<
      AtApiServiceEndpointsV2AdministrationJobsPutJobEndpointApiResponse,
      AtApiServiceEndpointsV2AdministrationJobsPutJobEndpointApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/jobs/${queryArg.entityId}`,
        method: "PUT",
        body: queryArg.atApiServiceEndpointsV2AdministrationModelsPutEntityRequestOfJobDtoAndJobId,
      }),
    }),
    atApiServiceEndpointsV2AdministrationJobsDeleteJobTriggerEndpoint:
      build.mutation<
        AtApiServiceEndpointsV2AdministrationJobsDeleteJobTriggerEndpointApiResponse,
        AtApiServiceEndpointsV2AdministrationJobsDeleteJobTriggerEndpointApiArg
      >({
        query: (queryArg) => ({
          url: `/api/v2/jobs/${queryArg.entityId1}/triggers/${queryArg.entityId2}`,
          method: "DELETE",
        }),
      }),
    atApiServiceEndpointsV2AdministrationJobsPutJobTriggerEndpoint:
      build.mutation<
        AtApiServiceEndpointsV2AdministrationJobsPutJobTriggerEndpointApiResponse,
        AtApiServiceEndpointsV2AdministrationJobsPutJobTriggerEndpointApiArg
      >({
        query: (queryArg) => ({
          url: `/api/v2/jobs/${queryArg.entityId1}/triggers/${queryArg.entityId2}`,
          method: "PUT",
          body: queryArg.atApiServiceEndpointsV2AdministrationModelsPutEntityRequestOfJobTriggerDtoAndJobIdAndJobTriggerId,
        }),
      }),
    atApiServiceEndpointsV2AdministrationJobsGetJobEndpoint: build.query<
      AtApiServiceEndpointsV2AdministrationJobsGetJobEndpointApiResponse,
      AtApiServiceEndpointsV2AdministrationJobsGetJobEndpointApiArg
    >({
      query: (queryArg) => ({ url: `/api/v2/jobs/${queryArg.rootEntityId}` }),
    }),
    atApiServiceEndpointsV2AdministrationJobsGetJobRunEndpoint: build.query<
      AtApiServiceEndpointsV2AdministrationJobsGetJobRunEndpointApiResponse,
      AtApiServiceEndpointsV2AdministrationJobsGetJobRunEndpointApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/jobs/jobRuns/${queryArg.rootEntityId}`,
      }),
    }),
    atApiServiceEndpointsV2AdministrationJobsGetJobRunLogsEndpoint: build.query<
      AtApiServiceEndpointsV2AdministrationJobsGetJobRunLogsEndpointApiResponse,
      AtApiServiceEndpointsV2AdministrationJobsGetJobRunLogsEndpointApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/jobs/jobRuns/${queryArg.jobRunId}/logs`,
        params: {
          $filter: queryArg.$filter,
          $orderby: queryArg.$orderby,
          $select: queryArg.$select,
          $top: queryArg.$top,
          $skip: queryArg.$skip,
          $count: queryArg.$count,
        },
      }),
    }),
    atApiServiceEndpointsV2AdministrationJobsGetJobRunsEndpoint: build.query<
      AtApiServiceEndpointsV2AdministrationJobsGetJobRunsEndpointApiResponse,
      AtApiServiceEndpointsV2AdministrationJobsGetJobRunsEndpointApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/jobs/jobRuns`,
        params: {
          $filter: queryArg.$filter,
          $orderby: queryArg.$orderby,
          $select: queryArg.$select,
          $top: queryArg.$top,
          $skip: queryArg.$skip,
          $count: queryArg.$count,
        },
      }),
    }),
    atApiServiceEndpointsV2AdministrationJobsGetJobsEndpoint: build.query<
      AtApiServiceEndpointsV2AdministrationJobsGetJobsEndpointApiResponse,
      AtApiServiceEndpointsV2AdministrationJobsGetJobsEndpointApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/jobs`,
        params: {
          $filter: queryArg.$filter,
          $orderby: queryArg.$orderby,
          $select: queryArg.$select,
          $top: queryArg.$top,
          $skip: queryArg.$skip,
          $count: queryArg.$count,
        },
      }),
    }),
    atApiServiceEndpointsV2AdministrationJobsPostJobEndpoint: build.mutation<
      AtApiServiceEndpointsV2AdministrationJobsPostJobEndpointApiResponse,
      AtApiServiceEndpointsV2AdministrationJobsPostJobEndpointApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/jobs`,
        method: "POST",
        body: queryArg.atApiServiceEndpointsV2AdministrationModelsJobDto,
      }),
    }),
    atApiServiceEndpointsV2AdministrationJobsGetJobTriggerEndpoint: build.query<
      AtApiServiceEndpointsV2AdministrationJobsGetJobTriggerEndpointApiResponse,
      AtApiServiceEndpointsV2AdministrationJobsGetJobTriggerEndpointApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/jobs/${queryArg.rootEntityId}/triggers/${queryArg.subEntityId1}`,
      }),
    }),
    atApiServiceEndpointsV2AdministrationJobsGetJobTriggersEndpoint:
      build.query<
        AtApiServiceEndpointsV2AdministrationJobsGetJobTriggersEndpointApiResponse,
        AtApiServiceEndpointsV2AdministrationJobsGetJobTriggersEndpointApiArg
      >({
        query: (queryArg) => ({
          url: `/api/v2/jobs/${queryArg.jobId}/triggers`,
          params: {
            $filter: queryArg.$filter,
            $orderby: queryArg.$orderby,
            $select: queryArg.$select,
            $top: queryArg.$top,
            $skip: queryArg.$skip,
            $count: queryArg.$count,
          },
        }),
      }),
    atApiServiceEndpointsV2AdministrationJobsPostJobTriggerEndpoint:
      build.mutation<
        AtApiServiceEndpointsV2AdministrationJobsPostJobTriggerEndpointApiResponse,
        AtApiServiceEndpointsV2AdministrationJobsPostJobTriggerEndpointApiArg
      >({
        query: (queryArg) => ({
          url: `/api/v2/jobs/${queryArg.jobId}/triggers`,
          method: "POST",
          body: queryArg.atApiServiceEndpointsV2AdministrationModelsJobTriggerDto,
        }),
      }),
  }),
  overrideExisting: false,
});
export { injectedRtkApi as administrationGeneratedApi };
export type AtApiServiceEndpointsV2AdministrationJobsDeleteJobEndpointApiResponse =
  unknown;
export type AtApiServiceEndpointsV2AdministrationJobsDeleteJobEndpointApiArg = {
  entityId: string;
};
export type AtApiServiceEndpointsV2AdministrationJobsPutJobEndpointApiResponse =
  unknown;
export type AtApiServiceEndpointsV2AdministrationJobsPutJobEndpointApiArg = {
  entityId: string;
  atApiServiceEndpointsV2AdministrationModelsPutEntityRequestOfJobDtoAndJobId: AtApiServiceEndpointsV2AdministrationModelsPutEntityRequestOfJobDtoAndJobId;
};
export type AtApiServiceEndpointsV2AdministrationJobsDeleteJobTriggerEndpointApiResponse =
  unknown;
export type AtApiServiceEndpointsV2AdministrationJobsDeleteJobTriggerEndpointApiArg =
  {
    entityId1: string;
    entityId2: string;
  };
export type AtApiServiceEndpointsV2AdministrationJobsPutJobTriggerEndpointApiResponse =
  unknown;
export type AtApiServiceEndpointsV2AdministrationJobsPutJobTriggerEndpointApiArg =
  {
    entityId1: string;
    entityId2: string;
    atApiServiceEndpointsV2AdministrationModelsPutEntityRequestOfJobTriggerDtoAndJobIdAndJobTriggerId: AtApiServiceEndpointsV2AdministrationModelsPutEntityRequestOfJobTriggerDtoAndJobIdAndJobTriggerId;
  };
export type AtApiServiceEndpointsV2AdministrationJobsGetJobEndpointApiResponse =
  /** status 200 Success */ AtApiServiceEndpointsV2AdministrationModelsGetEntityResponseOfJobDto;
export type AtApiServiceEndpointsV2AdministrationJobsGetJobEndpointApiArg = {
  rootEntityId: string;
};
export type AtApiServiceEndpointsV2AdministrationJobsGetJobRunEndpointApiResponse =
  /** status 200 Success */ AtApiServiceEndpointsV2AdministrationModelsGetEntityResponseOfJobRunDto;
export type AtApiServiceEndpointsV2AdministrationJobsGetJobRunEndpointApiArg = {
  rootEntityId: string;
};
export type AtApiServiceEndpointsV2AdministrationJobsGetJobRunLogsEndpointApiResponse =
  /** status 200 Success */ AtApiServiceEndpointsV2AdministrationModelsGetEntitiesResponse;
export type AtApiServiceEndpointsV2AdministrationJobsGetJobRunLogsEndpointApiArg =
  {
    jobRunId: string;
    $filter?: string | null;
    $orderby?: string | null;
    $select?: string | null;
    $top?: number | null;
    $skip?: number | null;
    $count?: boolean | null;
  };
export type AtApiServiceEndpointsV2AdministrationJobsGetJobRunsEndpointApiResponse =
  /** status 200 Success */ AtApiServiceEndpointsV2AdministrationModelsGetEntitiesResponse;
export type AtApiServiceEndpointsV2AdministrationJobsGetJobRunsEndpointApiArg =
  {
    $filter?: string | null;
    $orderby?: string | null;
    $select?: string | null;
    $top?: number | null;
    $skip?: number | null;
    $count?: boolean | null;
  };
export type AtApiServiceEndpointsV2AdministrationJobsGetJobsEndpointApiResponse =
  /** status 200 Success */ AtApiServiceEndpointsV2AdministrationModelsGetEntitiesResponse;
export type AtApiServiceEndpointsV2AdministrationJobsGetJobsEndpointApiArg = {
  $filter?: string | null;
  $orderby?: string | null;
  $select?: string | null;
  $top?: number | null;
  $skip?: number | null;
  $count?: boolean | null;
};
export type AtApiServiceEndpointsV2AdministrationJobsPostJobEndpointApiResponse =
  unknown;
export type AtApiServiceEndpointsV2AdministrationJobsPostJobEndpointApiArg = {
  atApiServiceEndpointsV2AdministrationModelsJobDto: AtApiServiceEndpointsV2AdministrationModelsJobDto;
};
export type AtApiServiceEndpointsV2AdministrationJobsGetJobTriggerEndpointApiResponse =
  /** status 200 Success */ AtApiServiceEndpointsV2AdministrationModelsGetEntityResponseOfJobTriggerDto;
export type AtApiServiceEndpointsV2AdministrationJobsGetJobTriggerEndpointApiArg =
  {
    rootEntityId: string;
    subEntityId1: string;
  };
export type AtApiServiceEndpointsV2AdministrationJobsGetJobTriggersEndpointApiResponse =
  /** status 200 Success */ AtApiServiceEndpointsV2AdministrationModelsGetEntitiesResponse;
export type AtApiServiceEndpointsV2AdministrationJobsGetJobTriggersEndpointApiArg =
  {
    jobId: string;
    $filter?: string | null;
    $orderby?: string | null;
    $select?: string | null;
    $top?: number | null;
    $skip?: number | null;
    $count?: boolean | null;
  };
export type AtApiServiceEndpointsV2AdministrationJobsPostJobTriggerEndpointApiResponse =
  unknown;
export type AtApiServiceEndpointsV2AdministrationJobsPostJobTriggerEndpointApiArg =
  {
    jobId: string;
    atApiServiceEndpointsV2AdministrationModelsJobTriggerDto: AtApiServiceEndpointsV2AdministrationModelsJobTriggerDto;
  };
export type AtApiServiceEndpointsV2AdministrationModelsJobDto = {
  id?: number;
  name?: string;
  description?: string | null;
  parameters?: string;
  type?: number;
  disabled?: boolean;
  successEmails?: string | null;
  errorEmails?: string | null;
};
export type AtApiServiceEndpointsV2AdministrationModelsPutEntityRequestOfJobDtoAndJobId =
  {
    model?: AtApiServiceEndpointsV2AdministrationModelsJobDto | null;
  };
export type AtApiServiceEndpointsV2AdministrationModelsJobTriggerDto = {
  id?: number;
  name?: string;
  description?: string | null;
  parameters?: string;
  type?: number;
  disabled?: boolean;
  jobId?: number;
  runParameters?: string | null;
};
export type AtApiServiceEndpointsV2AdministrationModelsPutEntityRequestOfJobTriggerDtoAndJobIdAndJobTriggerId =
  {
    model?: AtApiServiceEndpointsV2AdministrationModelsJobTriggerDto | null;
  };
export type AtApiServiceEndpointsV2AdministrationModelsGetEntityResponseOfJobDto =
  {
    entity?: AtApiServiceEndpointsV2AdministrationModelsJobDto | null;
  };
export type AtPrimitivesEnumsJobRunState = 0 | 1 | 2;
export type AtPrimitivesEnumsJobResultType = 0 | 1 | 2 | 3 | 4;
export type AtApiServiceEndpointsV2AdministrationModelsJobRunDto = {
  id?: number;
  jobId?: number;
  jobName?: string;
  state?: AtPrimitivesEnumsJobRunState;
  result?: AtPrimitivesEnumsJobResultType;
  triggered?: string;
  started?: string | null;
  finished?: string | null;
  authorId?: number | null;
};
export type AtApiServiceEndpointsV2AdministrationModelsGetEntityResponseOfJobRunDto =
  {
    entity?: AtApiServiceEndpointsV2AdministrationModelsJobRunDto | null;
  };
export type AtApiServiceEndpointsV2AdministrationModelsGetEntitiesResponse = {
  value?: {
    [key: string]: any;
  }[];
  count?: number | null;
};
export type AtApiServiceEndpointsV2AdministrationModelsGetEntityResponseOfJobTriggerDto =
  {
    entity?: AtApiServiceEndpointsV2AdministrationModelsJobTriggerDto | null;
  };
export const {
  useAtApiServiceEndpointsV2AdministrationJobsDeleteJobEndpointMutation,
  useAtApiServiceEndpointsV2AdministrationJobsPutJobEndpointMutation,
  useAtApiServiceEndpointsV2AdministrationJobsDeleteJobTriggerEndpointMutation,
  useAtApiServiceEndpointsV2AdministrationJobsPutJobTriggerEndpointMutation,
  useAtApiServiceEndpointsV2AdministrationJobsGetJobEndpointQuery,
  useAtApiServiceEndpointsV2AdministrationJobsGetJobRunEndpointQuery,
  useAtApiServiceEndpointsV2AdministrationJobsGetJobRunLogsEndpointQuery,
  useAtApiServiceEndpointsV2AdministrationJobsGetJobRunsEndpointQuery,
  useAtApiServiceEndpointsV2AdministrationJobsGetJobsEndpointQuery,
  useAtApiServiceEndpointsV2AdministrationJobsPostJobEndpointMutation,
  useAtApiServiceEndpointsV2AdministrationJobsGetJobTriggerEndpointQuery,
  useAtApiServiceEndpointsV2AdministrationJobsGetJobTriggersEndpointQuery,
  useAtApiServiceEndpointsV2AdministrationJobsPostJobTriggerEndpointMutation,
} = injectedRtkApi;
