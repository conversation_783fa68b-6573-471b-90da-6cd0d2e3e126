import "./chunk-LK32TJAX.js";

// node_modules/odata-filter-builder/es/ODataFilterBuilder.js
var canonicalFunctions = Object.freeze({
  __proto__: null,
  get canonicalFunction() {
    return canonicalFunction;
  },
  get contains() {
    return contains;
  },
  get startsWith() {
    return startsWith;
  },
  get endsWith() {
    return endsWith;
  },
  get toLower() {
    return toLower;
  },
  get toUpper() {
    return toUpper;
  },
  get trim() {
    return trim;
  },
  get substring() {
    return substring;
  },
  get concat() {
    return concat;
  },
  get length() {
    return length;
  },
  get indexOf() {
    return indexOf;
  }
});
function reduceSourceWithRule(source, rule, condition) {
  if (rule) {
    if (condition && source.condition !== condition) {
      source = {
        condition,
        // if has more then one rules
        // regroup source rules tree
        rules: source.rules.length > 1 ? [source] : source.rules
      };
    }
    source.rules.push(rule);
  }
  return source;
}
function inputRuleToString(rule) {
  if (typeof rule === "function") {
    rule = rule(new ODataFilterBuilder());
  }
  return rule && rule.toString();
}
function joinRulesWithCondition(rules, condition) {
  return rules.map(function(r) {
    return sourceRuleToString(r, true);
  }).join(" " + condition + " ");
}
function sourceRuleToString(rule, wrapInParenthesis) {
  if (wrapInParenthesis === void 0) {
    wrapInParenthesis = false;
  }
  if (typeof rule !== "string") {
    rule = rule.rules.length === 1 ? sourceRuleToString(rule.rules[0]) : joinRulesWithCondition(rule.rules, rule.condition);
  }
  return wrapInParenthesis ? "(" + rule + ")" : rule;
}
function inputFieldToString(field) {
  return typeof field === "function" ? field(canonicalFunctions) : field;
}
function isString(value) {
  return typeof value === "string";
}
function isDate(value) {
  return typeof value === "object" && Object.prototype.toString.call(value) === "[object Date]";
}
function normaliseValue(value) {
  if (isString(value)) {
    return "'" + value + "'";
  }
  if (isDate(value)) {
    return value.toISOString();
  }
  return value;
}
function canonicalFunction(functionName, field, values, normaliseValues, reverse) {
  if (normaliseValues === void 0) {
    normaliseValues = true;
  }
  if (reverse === void 0) {
    reverse = false;
  }
  field = inputFieldToString(field);
  if (typeof values === "undefined") {
    values = [];
  } else if (!Array.isArray(values)) {
    values = [values];
  }
  if (values.length === 0) {
    return functionName + "(" + field + ")";
  }
  if (normaliseValues) {
    values = values.map(normaliseValue);
  }
  var functionArgs = !reverse ? [field].concat(values) : [].concat(values, [field]);
  return functionName + "(" + functionArgs.join(", ") + ")";
}
function contains(field, value) {
  return canonicalFunction("contains", field, value);
}
function startsWith(field, value) {
  return canonicalFunction("startswith", field, value);
}
function endsWith(field, value) {
  return canonicalFunction("endswith", field, value);
}
function toLower(field) {
  return canonicalFunction("tolower", field);
}
function toUpper(field) {
  return canonicalFunction("toupper", field);
}
function trim(field) {
  return canonicalFunction("trim", field);
}
function substring(field) {
  for (var _len = arguments.length, values = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {
    values[_key - 1] = arguments[_key];
  }
  return canonicalFunction("substring", field, values);
}
function concat(field, value, normaliseValue2) {
  return canonicalFunction("concat", field, [value], normaliseValue2);
}
function length(field) {
  return canonicalFunction("length", field);
}
function indexOf(field, value) {
  return canonicalFunction("indexof", field, [value]);
}
function not(rule) {
  var ruleString = inputRuleToString(rule);
  if (ruleString) {
    return "not (" + ruleString + ")";
  }
}
function compare(field, operator, value, normaliseValue$1) {
  if (normaliseValue$1 === void 0) {
    normaliseValue$1 = true;
  }
  field = inputFieldToString(field);
  if (normaliseValue$1) {
    value = normaliseValue(value);
  }
  return field + " " + operator + " " + value;
}
function compareMap(field, operator, values, normaliseValues) {
  if (normaliseValues === void 0) {
    normaliseValues = true;
  }
  if (!values) {
    return [];
  }
  field = inputFieldToString(field);
  if (!Array.isArray(values)) {
    return [compare(field, operator, values, normaliseValues)];
  }
  return values.map(function(value) {
    return compare(field, operator, value, normaliseValues);
  });
}
function eq(field, value, normaliseValue2) {
  return compare(field, "eq", value, normaliseValue2);
}
function ne(field, value, normaliseValue2) {
  return compare(field, "ne", value, normaliseValue2);
}
function gt(field, value, normaliseValue2) {
  return compare(field, "gt", value, normaliseValue2);
}
function ge(field, value, normaliseValue2) {
  return compare(field, "ge", value, normaliseValue2);
}
function lt(field, value, normaliseValue2) {
  return compare(field, "lt", value, normaliseValue2);
}
function le(field, value, normaliseValue2) {
  return compare(field, "le", value, normaliseValue2);
}
function joinRules(rules, condition) {
  return rules.join(" " + condition + " ");
}
function compareIn(field, values, normaliseValues) {
  return joinRules(compareMap(field, "eq", values, normaliseValues), "or");
}
function compareAll(objectValue, normaliseValues) {
  var keys = Object.keys(objectValue);
  var rules = keys.filter(function(k) {
    return typeof objectValue[k] !== "undefined";
  }).map(function(field) {
    var value = objectValue[field];
    if (Array.isArray(value)) {
      return "(" + compareIn(field, value, normaliseValues) + ")";
    } else {
      return eq(field, value, normaliseValues);
    }
  });
  return joinRules(rules, "and");
}
function compareNotIn(field, values, normaliseValues) {
  return not(compareIn(field, values, normaliseValues));
}
var ODataFilterBuilder = function() {
  function ODataFilterBuilder2(condition) {
    if (condition === void 0) {
      condition = "and";
    }
    if (!(this instanceof ODataFilterBuilder2)) {
      return new ODataFilterBuilder2(condition);
    }
    this._condition = condition;
    this._source = {
      condition,
      rules: []
    };
  }
  var _proto = ODataFilterBuilder2.prototype;
  _proto._add = function _add(rule, condition) {
    if (condition === void 0) {
      condition = this._condition;
    }
    this._source = reduceSourceWithRule(this._source, inputRuleToString(rule), condition);
    return this;
  };
  _proto.and = function and(rule) {
    return this._add(rule, "and");
  };
  _proto.or = function or(rule) {
    return this._add(rule, "or");
  };
  _proto.not = function not$1(rule) {
    return this._add(not(rule));
  };
  _proto.eq = function eq$1(field, value, normaliseValue2) {
    return this._add(eq(field, value, normaliseValue2));
  };
  _proto.ne = function ne$1(field, value, normaliseValue2) {
    return this._add(ne(field, value, normaliseValue2));
  };
  _proto.gt = function gt$1(field, value, normaliseValue2) {
    return this._add(gt(field, value, normaliseValue2));
  };
  _proto.ge = function ge$1(field, value, normaliseValue2) {
    return this._add(ge(field, value, normaliseValue2));
  };
  _proto.lt = function lt$1(field, value, normaliseValue2) {
    return this._add(lt(field, value, normaliseValue2));
  };
  _proto.le = function le$1(field, value, normaliseValue2) {
    return this._add(le(field, value, normaliseValue2));
  };
  _proto["in"] = function _in(field, values, normaliseValues) {
    return this._add(compareIn(field, values, normaliseValues));
  };
  _proto.compareAll = function compareAll$1(objectValue, normaliseValues) {
    return this._add(compareAll(objectValue, normaliseValues));
  };
  _proto.notIn = function notIn(field, values, normaliseValues) {
    return this._add(compareNotIn(field, values, normaliseValues));
  };
  _proto.contains = function contains$1(field, value) {
    return this._add(contains(field, value));
  };
  _proto.startsWith = function startsWith$1(field, value) {
    return this._add(startsWith(field, value));
  };
  _proto.endsWith = function endsWith$1(field, value) {
    return this._add(endsWith(field, value));
  };
  _proto.fn = function fn(functionName, field, values, normaliseValues, reverse) {
    return this._add(canonicalFunction(functionName, field, values, normaliseValues, reverse));
  };
  _proto.isEmpty = function isEmpty() {
    return this._source.rules.length === 0;
  };
  _proto.toString = function toString() {
    return sourceRuleToString(this._source);
  };
  return ODataFilterBuilder2;
}();
ODataFilterBuilder.and = function() {
  return new ODataFilterBuilder("and");
};
ODataFilterBuilder.or = function() {
  return new ODataFilterBuilder("or");
};
ODataFilterBuilder.functions = canonicalFunctions;
var ODataFilterBuilder_default = ODataFilterBuilder;
export {
  ODataFilterBuilder,
  canonicalFunctions,
  ODataFilterBuilder_default as default
};
//# sourceMappingURL=odata-filter-builder.js.map
