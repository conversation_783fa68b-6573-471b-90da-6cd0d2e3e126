"use client";

import * as React from "react";
import { DataGridPremium, GridColDef, GridRenderCellParams, useGridApiRef } from "@mui/x-data-grid-premium";
import { JobTriggerDto, useDeleteJobTriggerMutation, useGetJobTriggersQuery } from "@/store/api/administration";
import { Button, Card, CardActions, CardContent, IconButton, Stack, Typography } from "@mui/material";
import { PencilSimpleIcon, TrashIcon } from "@phosphor-icons/react";
import { paths } from "@/paths";
import { RouterLink } from "@/components/core/link";
import { toast } from "@/components/core/toaster";

interface jobTriggersListProps {
    jobId: number;
};

export function JobTriggersList({jobId}: jobTriggersListProps): React.JSX.Element {
    const apiRef = useGridApiRef();
    const [allTriggers, setAllTriggers] = React.useState<JobTriggerDto[]>([]);
    const [deleteTrigger, deleteOptions] = useDeleteJobTriggerMutation();

    const {data} = useGetJobTriggersQuery({jobId: `${jobId}`});

    React.useEffect(() => {
        if (data && data.value != undefined){
            setAllTriggers(data.value);
        }
    }, [jobId, data, deleteOptions.isSuccess]);

    React.useEffect(() => {
            if (deleteOptions.isError){
                toast.error("Something went wrong!");
            }
        },
        [deleteOptions.isError]
    )

    const columns: GridColDef[] = [
        {
			field: 'id',
			headerName: 'Id',
            type: 'number',
            align: 'center',
            headerAlign: 'center',
            width: 50
		},
        {
			field: 'name',
			headerName: 'Name',
			type: 'string',
            minWidth: 125
		},
		{
			field: 'description',
			headerName: 'Description',
			minWidth: 250,
			type: 'string',
		},
        {
            field: 'parameters',
            headerName: 'Parameters',
            minWidth: 200,
            type: 'string',
            align: 'center',
            headerAlign: 'center'
        },
        {
			field: 'type',
			headerName: 'Type',
			minWidth: 150,
		},
        {
			field: 'disabled',
			headerName: 'Disabled',
			width: 100,
			type: 'boolean',
		},
        {
            field: 'actions',
            headerName: '',
            width: 100,
            align: 'center',
            headerAlign: 'center',
            sortable: false,
            disableColumnMenu: true,
            renderCell: (params: GridRenderCellParams) => {
                return (
                    <Stack direction="row" spacing={0.5} alignItems="center">
                        <IconButton
                            component={RouterLink} 
                            href={paths.administration.jobs.triggers.editTrigger({jobId, triggerId: params.row.id})}
                            size="small"
                            sx={{ padding: '4px' }}
                        >
                            <PencilSimpleIcon fontSize="small" />
                        </IconButton>
                        <IconButton
                            onClick={() => {
                                deleteTrigger({entityId1: `${jobId}`, entityId2: `${params.row.id}`});
                            }}
                            size="small"
                            sx={{ padding: '4px' }}
                        >
                            <TrashIcon fontSize="small" />
                        </IconButton>
                    </Stack>
                );
            },
        }
    ];

	return (
        <Card>
            <CardContent>
                <Typography> 
                    Triggers 
                </Typography>
                <DataGridPremium
                    apiRef={apiRef}
                    columns={columns}
                    rows={allTriggers}
                    pagination
                    pageSizeOptions={[5, 10, 25]}
                    initialState={{
                        pagination: { paginationModel: { pageSize: 10, page: 0 } },
                    }}
                    disableRowSelectionOnClick
                    rowHeight={52}
                    sx={{
                        mt: 2,
                        border: '1px solid #e0e0e0',
                        '& .MuiDataGrid-columnHeaders': {
                            backgroundColor: '#f5f5f5',
                            borderBottom: '1px solid #e0e0e0',
                            fontSize: '0.875rem',
                            fontWeight: 500,
                        },
                        '& .MuiDataGrid-columnHeader': {
                            padding: '8px 12px',
                        },
                        '& .MuiDataGrid-cell': {
                            padding: '8px 12px',
                            fontSize: '0.875rem',
                            borderBottom: '1px solid #f0f0f0',
                            display: 'flex',
                            alignItems: 'center',
                        },
                        '& .MuiDataGrid-cell:focus': {
                            outline: 'none',
                        },
                        '& .MuiDataGrid-cell:focus-within': {
                            outline: 'none',
                        },
                        '& .MuiDataGrid-row': {
                            '&:hover': {
                                backgroundColor: '#fafafa',
                            },
                        },
                        '& .MuiDataGrid-row:last-child .MuiDataGrid-cell': {
                            borderBottom: 'none',
                        },
                        '& .MuiDataGrid-footerContainer': {
                            borderTop: '1px solid #e0e0e0',
                            backgroundColor: '#fafafa',
                        },
                        '& .MuiDataGrid-columnSeparator': {
                            display: 'none',
                        },
                    }}
                />
            </CardContent>
            <CardActions>
                <Button 
                    style={{ marginLeft: 'auto' }}
                    component={RouterLink} 
                    href={paths.administration.jobs.triggers.createTrigger(jobId)}
                >
                    Create new trigger
                </Button>
            </CardActions>
        </Card>
	);
}
