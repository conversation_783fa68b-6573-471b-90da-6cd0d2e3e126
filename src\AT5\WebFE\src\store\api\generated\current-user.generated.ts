import { baseApi as api } from "../base-api";
const injectedRtkApi = api.injectEndpoints({
  endpoints: (build) => ({
    atApiServiceEndpointsV2CurrentUserGetBasicInfoEndpoint: build.query<
      AtApiServiceEndpointsV2CurrentUserGetBasicInfoEndpointApiResponse,
      AtApiServiceEndpointsV2CurrentUserGetBasicInfoEndpointApiArg
    >({
      query: () => ({ url: `/api/v2/currentuser/basicinfo` }),
    }),
    atApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpoint: build.query<
      AtApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpointApiResponse,
      AtApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpointApiArg
    >({
      query: () => ({ url: `/api/v2/demo/currentuserpermission` }),
    }),
  }),
  overrideExisting: false,
});
export { injectedRtkApi as currentUserGeneratedApi };
export type AtApiServiceEndpointsV2CurrentUserGetBasicInfoEndpointApiResponse =
  /** status 200 Success */ AtApiServiceEndpointsV2CurrentUserModelsCurrentUserBasicInfoResponse;
export type AtApiServiceEndpointsV2CurrentUserGetBasicInfoEndpointApiArg = void;
export type AtApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpointApiResponse =
  /** status 200 Success */ AtApiServiceDemoEndpointsModelsCurrentUserPermissionDemoResponse;
export type AtApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpointApiArg =
  void;
export type AtApiServiceEndpointsV2CurrentUserModelsCurrentUserBasicInfoResponse =
  {
    username?: string;
    firstName?: string;
    lastName?: string;
  };
export type AtApiServiceDemoEndpointsModelsCurrentUserPermissionDemoResponse = {
  hasPermissionEnum?: boolean;
  hasPermissionSmartEnum?: boolean;
};
export const {
  useAtApiServiceEndpointsV2CurrentUserGetBasicInfoEndpointQuery,
  useAtApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpointQuery,
} = injectedRtkApi;
