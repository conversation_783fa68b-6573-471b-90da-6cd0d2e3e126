import {
  usersGeneratedApi,
  useAtApiServiceEndpointsV2UsersGetUserEndpointQuery,
} from './generated/users.generated';

// Re-export only types, not the verbose hooks
export type * from './generated/users.generated';

// Enhanced API with proper cache tags using enhanceEndpoints
export const usersApi = usersGeneratedApi.enhanceEndpoints({
  addTagTypes: ['User'],
  endpoints: {
    atApiServiceEndpointsV2UsersGetUserEndpoint: {
      providesTags: (result, error, arg) => [{ type: 'User', id: arg.userId }],
    },
  },
});

// Clean hook exports that map to the verbose generated hook names
export const useGetUserQuery = useAtApiServiceEndpointsV2UsersGetUserEndpointQuery;
