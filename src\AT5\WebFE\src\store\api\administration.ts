import {
  administrationGenerated<PERSON>pi,
  useAtApiServiceEndpointsV2AdministrationJobsGetJobsEndpointQuery,
  useAtApiServiceEndpointsV2AdministrationJobsGetJobEndpointQuery,
  useAtApiServiceEndpointsV2AdministrationJobsPostJobEndpointMutation,
  useAtApiServiceEndpointsV2AdministrationJobsPutJobEndpointMutation,
  useAtApiServiceEndpointsV2AdministrationJobsDeleteJobEndpointMutation,
  useAtApiServiceEndpointsV2AdministrationJobsGetJobTriggersEndpointQuery,
  useAtApiServiceEndpointsV2AdministrationJobsGetJobTriggerEndpointQuery,
  useAtApiServiceEndpointsV2AdministrationJobsPostJobTriggerEndpointMutation,
  useAtApiServiceEndpointsV2AdministrationJobsPutJobTriggerEndpointMutation,
  useAtApiServiceEndpointsV2AdministrationJobsDeleteJobTriggerEndpointMutation,
  useAtApiServiceEndpointsV2AdministrationJobsGetJobRunEndpointQuery,
  useAtApiServiceEndpointsV2AdministrationJobsGetJobRunLogsEndpointQuery,
} from './generated/administration.generated';

// Re-export only types and the enhanced API, not the verbose hooks
export type * from './generated/administration.generated';

// Enhanced API with proper cache tags using enhanceEndpoints
export const administrationApi = administrationGeneratedApi.enhanceEndpoints({
  addTagTypes: ['Job', 'JobTrigger'],
  endpoints: {
    // Job Management Endpoints
    atApiServiceEndpointsV2AdministrationJobsGetJobsEndpoint: {
      providesTags: ['Job'],
    },
    atApiServiceEndpointsV2AdministrationJobsGetJobEndpoint: {
      providesTags: (result, error, arg) => [{ type: 'Job', id: arg.rootEntityId }],
    },
    atApiServiceEndpointsV2AdministrationJobsPostJobEndpoint: {
      invalidatesTags: ['Job'],
    },
    atApiServiceEndpointsV2AdministrationJobsPutJobEndpoint: {
      invalidatesTags: (result, error, arg) => [
        { type: 'Job', id: arg.entityId },
        'Job',
      ],
    },
    atApiServiceEndpointsV2AdministrationJobsDeleteJobEndpoint: {
      invalidatesTags: (result, error, arg) => [
        { type: 'Job', id: arg.entityId },
        'Job',
      ],
    },

    // Job Trigger Management Endpoints
    atApiServiceEndpointsV2AdministrationJobsGetJobTriggersEndpoint: {
      providesTags: (result, error, arg) => [
        { type: 'JobTrigger', id: `job-${arg.jobId}` },
        'JobTrigger',
      ],
    },
    atApiServiceEndpointsV2AdministrationJobsGetJobTriggerEndpoint: {
      providesTags: (result, error, arg) => [
        { type: 'JobTrigger', id: arg.subEntityId1 },
      ],
    },
    atApiServiceEndpointsV2AdministrationJobsPostJobTriggerEndpoint: {
      invalidatesTags: (result, error, arg) => [
        { type: 'JobTrigger', id: `job-${arg.jobId}` },
        'JobTrigger',
      ],
    },
    atApiServiceEndpointsV2AdministrationJobsPutJobTriggerEndpoint: {
      invalidatesTags: (result, error, arg) => [
        { type: 'JobTrigger', id: arg.entityId2 },
        { type: 'JobTrigger', id: `job-${arg.entityId1}` },
        'JobTrigger',
      ],
    },
    atApiServiceEndpointsV2AdministrationJobsDeleteJobTriggerEndpoint: {
      invalidatesTags: (result, error, arg) => [
        { type: 'JobTrigger', id: arg.entityId2 },
        { type: 'JobTrigger', id: `job-${arg.entityId1}` },
        'JobTrigger',
      ],
    },
  },
});

// Clean hook exports that map to the verbose generated hook names
export const useGetJobsQuery = useAtApiServiceEndpointsV2AdministrationJobsGetJobsEndpointQuery;
export const useGetJobQuery = useAtApiServiceEndpointsV2AdministrationJobsGetJobEndpointQuery;
export const useGetJobRunQuery = useAtApiServiceEndpointsV2AdministrationJobsGetJobRunEndpointQuery;
export const useGetJobRunLogsQuery = useAtApiServiceEndpointsV2AdministrationJobsGetJobRunLogsEndpointQuery;
export const useCreateJobMutation = useAtApiServiceEndpointsV2AdministrationJobsPostJobEndpointMutation;
export const useUpdateJobMutation = useAtApiServiceEndpointsV2AdministrationJobsPutJobEndpointMutation;
export const useDeleteJobMutation = useAtApiServiceEndpointsV2AdministrationJobsDeleteJobEndpointMutation;

// Job Trigger hooks
export const useGetJobTriggersQuery = useAtApiServiceEndpointsV2AdministrationJobsGetJobTriggersEndpointQuery;
export const useGetJobTriggerQuery = useAtApiServiceEndpointsV2AdministrationJobsGetJobTriggerEndpointQuery;
export const useCreateJobTriggerMutation = useAtApiServiceEndpointsV2AdministrationJobsPostJobTriggerEndpointMutation;
export const useUpdateJobTriggerMutation = useAtApiServiceEndpointsV2AdministrationJobsPutJobTriggerEndpointMutation;
export const useDeleteJobTriggerMutation = useAtApiServiceEndpointsV2AdministrationJobsDeleteJobTriggerEndpointMutation;
