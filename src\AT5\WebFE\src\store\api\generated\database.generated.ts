import { baseApi as api } from "../base-api";
const injectedRtkApi = api.injectEndpoints({
  endpoints: (build) => ({
    atApiServiceEndpointsV2DatabaseDatabaseNamesEndpoint: build.query<
      AtApiServiceEndpointsV2DatabaseDatabaseNamesEndpointApiResponse,
      AtApiServiceEndpointsV2DatabaseDatabaseNamesEndpointApiArg
    >({
      query: () => ({ url: `/api/v2/database/namestest` }),
    }),
  }),
  overrideExisting: false,
});
export { injectedRtkApi as databaseGeneratedApi };
export type AtApiServiceEndpointsV2DatabaseDatabaseNamesEndpointApiResponse =
  /** status 200 Success */ AtApiServiceEndpointsV2DatabaseModelsDatabaseNamesResponse;
export type AtApiServiceEndpointsV2DatabaseDatabaseNamesEndpointApiArg = void;
export type AtApiServiceEndpointsV2DatabaseModelsDatabaseNamesResponse = {
  masterDbName?: string;
  organizationDbName?: string;
};
export const { useAtApiServiceEndpointsV2DatabaseDatabaseNamesEndpointQuery } =
  injectedRtkApi;
