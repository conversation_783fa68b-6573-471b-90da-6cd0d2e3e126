import { baseApi as api } from "../base-api";
const injectedRtkApi = api.injectEndpoints({
  endpoints: (build) => ({
    atApiServiceDemoEndpointsDeleteCityEndpoint: build.mutation<
      AtApiServiceDemoEndpointsDeleteCityEndpointApiResponse,
      AtApiServiceDemoEndpointsDeleteCityEndpointApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/demo/cities/${queryArg.cityId}`,
        method: "DELETE",
      }),
    }),
    atApiServiceDemoEndpointsGetCityEndpoint: build.query<
      AtApiServiceDemoEndpointsGetCityEndpointApiResponse,
      AtApiServiceDemoEndpointsGetCityEndpointApiArg
    >({
      query: (queryArg) => ({ url: `/api/v2/demo/cities/${queryArg.cityId}` }),
    }),
    atApiServiceDemoEndpointsForecastEndpoint: build.query<
      AtApiServiceDemoEndpointsForecastEndpointApiResponse,
      AtApiServiceDemoEndpointsForecastEndpointApiArg
    >({
      query: () => ({ url: `/api/v2/demo/forecast` }),
    }),
    atApiServiceDemoEndpointsGetCitiesEndpoint: build.query<
      AtApiServiceDemoEndpointsGetCitiesEndpointApiResponse,
      AtApiServiceDemoEndpointsGetCitiesEndpointApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/demo/cities`,
        params: {
          minId: queryArg.minId,
          maxId: queryArg.maxId,
        },
      }),
    }),
    atApiServiceDemoEndpointsPostCityEndpoint: build.mutation<
      AtApiServiceDemoEndpointsPostCityEndpointApiResponse,
      AtApiServiceDemoEndpointsPostCityEndpointApiArg
    >({
      query: (queryArg) => ({
        url: `/api/v2/demo/cities`,
        method: "POST",
        body: queryArg.atApiServiceDemoEndpointsModelsPostCityRequest,
      }),
    }),
    atApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpoint: build.query<
      AtApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpointApiResponse,
      AtApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpointApiArg
    >({
      query: () => ({ url: `/api/v2/demo/currentuserpermission` }),
    }),
  }),
  overrideExisting: false,
});
export { injectedRtkApi as demoGeneratedApi };
export type AtApiServiceDemoEndpointsDeleteCityEndpointApiResponse = unknown;
export type AtApiServiceDemoEndpointsDeleteCityEndpointApiArg = {
  cityId: number;
};
export type AtApiServiceDemoEndpointsGetCityEndpointApiResponse =
  /** status 200 Success */ AtApiServiceDemoEndpointsModelsGetCityResponse;
export type AtApiServiceDemoEndpointsGetCityEndpointApiArg = {
  cityId: number;
};
export type AtApiServiceDemoEndpointsForecastEndpointApiResponse =
  /** status 200 Success */ AtApiServiceDemoEndpointsModelsForecastResponse;
export type AtApiServiceDemoEndpointsForecastEndpointApiArg = void;
export type AtApiServiceDemoEndpointsGetCitiesEndpointApiResponse =
  /** status 200 Success */ AtApiServiceDemoEndpointsModelsGetCitiesResponse;
export type AtApiServiceDemoEndpointsGetCitiesEndpointApiArg = {
  minId?: number | null;
  maxId?: number | null;
};
export type AtApiServiceDemoEndpointsPostCityEndpointApiResponse =
  /** status 200 Success */ AtApiServiceDemoEndpointsModelsPostCityResponse;
export type AtApiServiceDemoEndpointsPostCityEndpointApiArg = {
  atApiServiceDemoEndpointsModelsPostCityRequest: AtApiServiceDemoEndpointsModelsPostCityRequest;
};
export type AtApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpointApiResponse =
  /** status 200 Success */ AtApiServiceDemoEndpointsModelsCurrentUserPermissionDemoResponse;
export type AtApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpointApiArg =
  void;
export type AtApiServiceDemoEndpointsModelsCity = {
  id?: number;
  name?: string;
  population?: number;
};
export type AtApiServiceDemoEndpointsModelsGetCityResponse = {
  city?: AtApiServiceDemoEndpointsModelsCity;
};
export type AtApiServiceDemoEndpointsModelsForecast = {
  date?: string;
  temperature?: number;
  summary?: string;
};
export type AtApiServiceDemoEndpointsModelsForecastResponse = {
  forecasts?: AtApiServiceDemoEndpointsModelsForecast[];
};
export type AtApiServiceDemoEndpointsModelsGetCitiesResponse = {
  cities?: AtApiServiceDemoEndpointsModelsCity[];
};
export type AtApiServiceDemoEndpointsModelsPostCityResponse = {
  city?: AtApiServiceDemoEndpointsModelsCity;
};
export type AtApiServiceDemoEndpointsModelsPostCityRequest = {
  name?: string;
  population?: number;
};
export type AtApiServiceDemoEndpointsModelsCurrentUserPermissionDemoResponse = {
  hasPermissionEnum?: boolean;
  hasPermissionSmartEnum?: boolean;
};
export const {
  useAtApiServiceDemoEndpointsDeleteCityEndpointMutation,
  useAtApiServiceDemoEndpointsGetCityEndpointQuery,
  useAtApiServiceDemoEndpointsForecastEndpointQuery,
  useAtApiServiceDemoEndpointsGetCitiesEndpointQuery,
  useAtApiServiceDemoEndpointsPostCityEndpointMutation,
  useAtApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpointQuery,
} = injectedRtkApi;
