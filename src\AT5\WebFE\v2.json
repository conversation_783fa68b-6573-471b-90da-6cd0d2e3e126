{"x-generator": "NSwag v14.2.0.0 (NJsonSchema v11.1.0.0 (Newtonsoft.Json v13.0.0.0))", "openapi": "3.0.0", "info": {"title": "ApiService", "version": "2"}, "servers": [{"url": "https://localhost:7488"}], "paths": {"/api/v2/users/{userId}": {"get": {"tags": ["Users"], "operationId": "ATApiServiceEndpointsV2UsersGetUserEndpoint", "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsV2UsersModelsGetUserResponse"}}}}}}}, "/api/v2/database/namestest": {"get": {"tags": ["Database"], "operationId": "ATApiServiceEndpointsV2DatabaseDatabaseNamesEndpoint", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsV2DatabaseModelsDatabaseNamesResponse"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/currentuser/basicinfo": {"get": {"tags": ["Current<PERSON>"], "operationId": "ATApiServiceEndpointsV2CurrentUserGetBasicInfoEndpoint", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsV2CurrentUserModelsCurrentUserBasicInfoResponse"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/currenttenant/basicinfo": {"get": {"tags": ["Currenttenant"], "operationId": "ATApiServiceEndpointsV2CurrentTenantGetBasicInfoEndpoint", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsV2CurrentTenantModelsCurrentTenantBasicInfoResponse"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/jobs/{entityId}": {"delete": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsV2AdministrationJobsDeleteJobEndpoint", "parameters": [{"name": "entityId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content"}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}, "put": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsV2AdministrationJobsPutJobEndpoint", "parameters": [{"name": "entityId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"x-name": "PutEntityRequest`2", "description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsV2AdministrationModelsPutEntityRequestOfJobDtoAndJobId"}}}, "required": true, "x-position": 1}, "responses": {"204": {"description": "No Content"}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/jobs/{entityId1}/triggers/{entityId2}": {"delete": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsV2AdministrationJobsDeleteJobTriggerEndpoint", "parameters": [{"name": "entityId1", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "entityId2", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"204": {"description": "No Content"}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}, "put": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsV2AdministrationJobsPutJobTriggerEndpoint", "parameters": [{"name": "entityId1", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "entityId2", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"x-name": "PutEntityRequest`3", "description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsV2AdministrationModelsPutEntityRequestOfJobTriggerDtoAndJobIdAndJobTriggerId"}}}, "required": true, "x-position": 1}, "responses": {"204": {"description": "No Content"}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/jobs/{rootEntityId}": {"get": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsV2AdministrationJobsGetJobEndpoint", "parameters": [{"name": "rootEntityId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsV2AdministrationModelsGetEntityResponseOfJobDto"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/jobs/jobRuns/{rootEntityId}": {"get": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsV2AdministrationJobsGetJobRunEndpoint", "parameters": [{"name": "rootEntityId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsV2AdministrationModelsGetEntityResponseOfJobRunDto"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/jobs/jobRuns/{jobRunId}/logs": {"get": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsV2AdministrationJobsGetJobRunLogsEndpoint", "parameters": [{"name": "jobRunId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "$filter", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$orderby", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$select", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$top", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "$skip", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "$count", "in": "query", "schema": {"type": "boolean", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsV2AdministrationModelsGetEntitiesResponse"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/jobs/jobRuns": {"get": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsV2AdministrationJobsGetJobRunsEndpoint", "parameters": [{"name": "$filter", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$orderby", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$select", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$top", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "$skip", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "$count", "in": "query", "schema": {"type": "boolean", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsV2AdministrationModelsGetEntitiesResponse"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/jobs": {"get": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsV2AdministrationJobsGetJobsEndpoint", "parameters": [{"name": "$filter", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$orderby", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$select", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$top", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "$skip", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "$count", "in": "query", "schema": {"type": "boolean", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsV2AdministrationModelsGetEntitiesResponse"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}, "post": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsV2AdministrationJobsPostJobEndpoint", "requestBody": {"x-name": "job", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsV2AdministrationModelsJobDto"}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/jobs/{rootEntityId}/triggers/{subEntityId1}": {"get": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsV2AdministrationJobsGetJobTriggerEndpoint", "parameters": [{"name": "rootEntityId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "subEntityId1", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsV2AdministrationModelsGetEntityResponseOfJobTriggerDto"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/jobs/{jobId}/triggers": {"get": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsV2AdministrationJobsGetJobTriggersEndpoint", "parameters": [{"name": "jobId", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "$filter", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$orderby", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$select", "in": "query", "schema": {"type": "string", "nullable": true}}, {"name": "$top", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "$skip", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "$count", "in": "query", "schema": {"type": "boolean", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsV2AdministrationModelsGetEntitiesResponse"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}, "post": {"tags": ["Jobs"], "operationId": "ATApiServiceEndpointsV2AdministrationJobsPostJobTriggerEndpoint", "parameters": [{"name": "jobId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"x-name": "job<PERSON><PERSON>ger", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceEndpointsV2AdministrationModelsJobTriggerDto"}}}, "required": true}, "responses": {"204": {"description": "No Content"}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/demo/cities/{cityId}": {"delete": {"tags": ["Demo"], "operationId": "ATApiServiceDemoEndpointsDeleteCityEndpoint", "parameters": [{"name": "cityId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"204": {"description": "No Content"}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}, "get": {"tags": ["Demo"], "operationId": "ATApiServiceDemoEndpointsGetCityEndpoint", "parameters": [{"name": "cityId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceDemoEndpointsModelsGetCityResponse"}}}}}}}, "/api/v2/demo/forecast": {"get": {"tags": ["Demo"], "operationId": "ATApiServiceDemoEndpointsForecastEndpoint", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceDemoEndpointsModelsForecastResponse"}}}}}}}, "/api/v2/demo/cities": {"get": {"tags": ["Demo"], "operationId": "ATApiServiceDemoEndpointsGetCitiesEndpoint", "parameters": [{"name": "minId", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}, {"name": "maxId", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}}], "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceDemoEndpointsModelsGetCitiesResponse"}}}}}}, "post": {"tags": ["Demo"], "operationId": "ATApiServiceDemoEndpointsPostCityEndpoint", "requestBody": {"x-name": "PostCityRequest", "description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceDemoEndpointsModelsPostCityRequest"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceDemoEndpointsModelsPostCityResponse"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}, "/api/v2/demo/currentuserpermission": {"get": {"tags": ["Demo"], "operationId": "ATApiServiceDemoEndpointsGetCurrentUserPermissionDemoEndpoint", "responses": {"200": {"description": "Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ATApiServiceDemoEndpointsModelsCurrentUserPermissionDemoResponse"}}}}, "401": {"description": "Unauthorized"}}, "security": [{"JWTBearerAuth": []}]}}}, "components": {"schemas": {"ATApiServiceEndpointsV2UsersModelsGetUserResponse": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "userId": {"type": "string"}}}, "ATApiServiceEndpointsV2UsersModelsGetUserRequest": {"type": "object", "additionalProperties": false}, "ATApiServiceEndpointsV2DatabaseModelsDatabaseNamesResponse": {"type": "object", "additionalProperties": false, "properties": {"masterDbName": {"type": "string"}, "organizationDbName": {"type": "string"}}}, "ATApiServiceEndpointsV2CurrentUserModelsCurrentUserBasicInfoResponse": {"type": "object", "additionalProperties": false, "properties": {"username": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}}}, "ATApiServiceEndpointsV2CurrentTenantModelsCurrentTenantBasicInfoResponse": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "headerTitle": {"type": "string"}, "headerColor": {"type": "string", "nullable": true}}}, "ATApiServiceEndpointsV2AdministrationModelsDeleteEntityRequestOfJobId": {"type": "object", "additionalProperties": false}, "ATApiServiceEndpointsV2AdministrationModelsDeleteEntityRequestOfJobIdAndJobTriggerId": {"type": "object", "additionalProperties": false}, "ATApiServiceEndpointsV2AdministrationModelsGetEntityResponseOfJobDto": {"type": "object", "additionalProperties": false, "properties": {"entity": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ATApiServiceEndpointsV2AdministrationModelsJobDto"}]}}}, "ATApiServiceEndpointsV2AdministrationModelsJobDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "parameters": {"type": "string"}, "type": {"type": "integer", "format": "int32"}, "disabled": {"type": "boolean"}, "successEmails": {"type": "string", "nullable": true}, "errorEmails": {"type": "string", "nullable": true}}}, "ATApiServiceEndpointsV2AdministrationModelsGetEntityRequestOfJobId": {"type": "object", "additionalProperties": false}, "ATApiServiceEndpointsV2AdministrationModelsGetEntityResponseOfJobRunDto": {"type": "object", "additionalProperties": false, "properties": {"entity": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ATApiServiceEndpointsV2AdministrationModelsJobRunDto"}]}}}, "ATApiServiceEndpointsV2AdministrationModelsJobRunDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "jobId": {"type": "integer", "format": "int32"}, "jobName": {"type": "string"}, "state": {"$ref": "#/components/schemas/ATPrimitivesEnumsJobRunState"}, "result": {"$ref": "#/components/schemas/ATPrimitivesEnumsJobResultType"}, "triggered": {"type": "string", "format": "date-time"}, "started": {"type": "string", "format": "date-time", "nullable": true}, "finished": {"type": "string", "format": "date-time", "nullable": true}, "authorId": {"type": "integer", "format": "int32", "nullable": true}}}, "ATPrimitivesEnumsJobRunState": {"type": "integer", "description": "", "x-enumNames": ["Starting", "Running", "Finished"], "enum": [0, 1, 2]}, "ATPrimitivesEnumsJobResultType": {"type": "integer", "description": "", "x-enumNames": ["None", "Success", "SuccessWithWarning", "PartialSuccess", "Failure"], "enum": [0, 1, 2, 3, 4]}, "ATApiServiceEndpointsV2AdministrationModelsGetEntityRequestOfJobRunId": {"type": "object", "additionalProperties": false}, "ATApiServiceEndpointsV2AdministrationModelsGetEntitiesResponse": {"type": "object", "additionalProperties": false, "properties": {"value": {"type": "array", "items": {"type": "object", "additionalProperties": {}}}, "count": {"type": "integer", "format": "int64", "nullable": true}}}, "ATApiServiceEndpointsV2AdministrationModelsGetEntitiesRequest": {"type": "object", "additionalProperties": false}, "ATApiServiceEndpointsV2AdministrationModelsGetEntityResponseOfJobTriggerDto": {"type": "object", "additionalProperties": false, "properties": {"entity": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ATApiServiceEndpointsV2AdministrationModelsJobTriggerDto"}]}}}, "ATApiServiceEndpointsV2AdministrationModelsJobTriggerDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "description": {"type": "string", "nullable": true}, "parameters": {"type": "string"}, "type": {"type": "integer", "format": "int32"}, "disabled": {"type": "boolean"}, "jobId": {"type": "integer", "format": "int32"}, "runParameters": {"type": "string", "nullable": true}}}, "ATApiServiceEndpointsV2AdministrationModelsGetEntityRequestOfJobIdAndJobTriggerId": {"type": "object", "additionalProperties": false}, "ATApiServiceEndpointsV2AdministrationModelsPostJobRequest": {"type": "object", "additionalProperties": false, "properties": {"job": {"$ref": "#/components/schemas/ATApiServiceEndpointsV2AdministrationModelsJobDto"}}}, "ATApiServiceEndpointsV2AdministrationModelsPostJobTriggerRequest": {"type": "object", "additionalProperties": false, "properties": {"jobTrigger": {"$ref": "#/components/schemas/ATApiServiceEndpointsV2AdministrationModelsJobTriggerDto"}}}, "ATApiServiceEndpointsV2AdministrationModelsPutEntityRequestOfJobDtoAndJobId": {"type": "object", "additionalProperties": false, "properties": {"model": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ATApiServiceEndpointsV2AdministrationModelsJobDto"}]}}}, "ATApiServiceEndpointsV2AdministrationModelsPutEntityRequestOfJobTriggerDtoAndJobIdAndJobTriggerId": {"type": "object", "additionalProperties": false, "properties": {"model": {"nullable": true, "oneOf": [{"$ref": "#/components/schemas/ATApiServiceEndpointsV2AdministrationModelsJobTriggerDto"}]}}}, "ATApiServiceDemoEndpointsModelsDeleteCityRequest": {"type": "object", "additionalProperties": false}, "ATApiServiceDemoEndpointsModelsForecastResponse": {"type": "object", "additionalProperties": false, "properties": {"forecasts": {"type": "array", "items": {"$ref": "#/components/schemas/ATApiServiceDemoEndpointsModelsForecast"}}}}, "ATApiServiceDemoEndpointsModelsForecast": {"type": "object", "additionalProperties": false, "properties": {"date": {"type": "string", "format": "date"}, "temperature": {"type": "integer", "format": "int32"}, "summary": {"type": "string"}}}, "ATApiServiceDemoEndpointsModelsGetCitiesResponse": {"type": "object", "additionalProperties": false, "properties": {"cities": {"type": "array", "items": {"$ref": "#/components/schemas/ATApiServiceDemoEndpointsModelsCity"}}}}, "ATApiServiceDemoEndpointsModelsCity": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string"}, "population": {"type": "integer", "format": "int32"}}}, "ATApiServiceDemoEndpointsModelsGetCitiesRequest": {"type": "object", "additionalProperties": false}, "ATApiServiceDemoEndpointsModelsGetCityResponse": {"type": "object", "additionalProperties": false, "properties": {"city": {"$ref": "#/components/schemas/ATApiServiceDemoEndpointsModelsCity"}}}, "ATApiServiceDemoEndpointsModelsGetCityRequest": {"type": "object", "additionalProperties": false}, "ATApiServiceDemoEndpointsModelsCurrentUserPermissionDemoResponse": {"type": "object", "additionalProperties": false, "properties": {"hasPermissionEnum": {"type": "boolean"}, "hasPermissionSmartEnum": {"type": "boolean"}}}, "ATApiServiceDemoEndpointsModelsPostCityResponse": {"type": "object", "additionalProperties": false, "properties": {"city": {"$ref": "#/components/schemas/ATApiServiceDemoEndpointsModelsCity"}}}, "ATApiServiceDemoEndpointsModelsPostCityRequest": {"type": "object", "additionalProperties": false, "properties": {"name": {"type": "string"}, "population": {"type": "integer", "format": "int32"}}}}, "securitySchemes": {"JWTBearerAuth": {"type": "http", "description": "Enter a JWT token to authorize the requests...", "scheme": "Bearer", "bearerFormat": "JWT"}}}}